/**
 * Module API Client pour le tableau de bord opérateur
 * Gestion centralisée des requêtes API avec gestion d'erreurs
 */

class OperatorApiClient {
    constructor() {
        this.baseUrl = API_BASE_URL || '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json'
        };
    }

    /**
     * Effectue une requête API avec gestion d'erreurs
     * @param {string} endpoint - L'endpoint de l'API
     * @param {Object} options - Options de la requête
     * @returns {Promise<Object>} - Réponse de l'API
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}/${endpoint}`;
        const token = getAuthToken();
        
        const finalOptions = {
            headers: {
                ...this.defaultHeaders,
                ...(token && { 'Authorization': `Bearer ${token}` }),
                ...options.headers
            },
            ...options
        };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `Erreur HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error(`Erreur API [${endpoint}]:`, error);
            throw error;
        }
    }

    /**
     * Requête GET
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url);
    }

    /**
     * Requête POST
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * Requête PUT
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * Requête DELETE
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }

    // ==================== MÉTHODES SPÉCIFIQUES OPÉRATEUR ====================

    /**
     * Récupère les données du tableau de bord
     */
    async getDashboardData() {
        return this.get('operator/dashboard');
    }

    /**
     * Récupère les données d'analytics
     */
    async getAnalytics() {
        return this.get('operator/analytics');
    }

    /**
     * Récupère les lieux
     */
    async getLocations() {
        return this.get('operator/locations');
    }

    /**
     * Crée un nouveau lieu
     */
    async createLocation(locationData) {
        return this.post('operator/locations', locationData);
    }

    /**
     * Met à jour un lieu
     */
    async updateLocation(locationId, locationData) {
        return this.put(`operator/locations/${locationId}`, locationData);
    }

    /**
     * Supprime un lieu
     */
    async deleteLocation(locationId) {
        return this.delete(`operator/locations/${locationId}`);
    }

    /**
     * Récupère les arrêts
     */
    async getStops() {
        return this.get('operator/stops');
    }

    /**
     * Crée un nouvel arrêt
     */
    async createStop(stopData) {
        return this.post('operator/stops', stopData);
    }

    /**
     * Met à jour un arrêt
     */
    async updateStop(stopId, stopData) {
        return this.put(`operator/stops/${stopId}`, stopData);
    }

    /**
     * Supprime un arrêt
     */
    async deleteStop(stopId) {
        return this.delete(`operator/stops/${stopId}`);
    }

    /**
     * Récupère les commodités
     */
    async getAmenities() {
        return this.get('operator/amenities');
    }

    /**
     * Crée une nouvelle commodité
     */
    async createAmenity(amenityData) {
        return this.post('operator/amenities', amenityData);
    }

    /**
     * Met à jour une commodité
     */
    async updateAmenity(amenityId, amenityData) {
        return this.put(`operator/amenities/${amenityId}`, amenityData);
    }

    /**
     * Supprime une commodité
     */
    async deleteAmenity(amenityId) {
        return this.delete(`operator/amenities/${amenityId}`);
    }

    /**
     * Récupère les itinéraires
     */
    async getRoutes() {
        return this.get('operator/routes');
    }

    /**
     * Crée un nouvel itinéraire
     */
    async createRoute(routeData) {
        return this.post('operator/routes', routeData);
    }

    /**
     * Met à jour un itinéraire
     */
    async updateRoute(routeId, routeData) {
        return this.put(`operator/routes/${routeId}`, routeData);
    }

    /**
     * Supprime un itinéraire
     */
    async deleteRoute(routeId) {
        return this.delete(`operator/routes/${routeId}`);
    }

    /**
     * Récupère les voyages
     */
    async getTrips(filters = {}) {
        return this.get('operator/trips', filters);
    }

    /**
     * Récupère les bus
     */
    async getBuses() {
        return this.get('operator/buses');
    }

    /**
     * Récupère les réservations
     */
    async getBookings(filters = {}) {
        return this.get('operator/bookings', filters);
    }

    /**
     * Récupère les paiements
     */
    async getPayments(filters = {}) {
        return this.get('operator/payments', filters);
    }

    /**
     * Récupère les utilisateurs
     */
    async getUsers(filters = {}) {
        return this.get('operator/users', filters);
    }

    /**
     * Récupère la tarification
     */
    async getPricing() {
        return this.get('operator/pricing');
    }

    /**
     * Valide un ticket
     */
    async validateTicket(ticketCode) {
        return this.post('tickets/validate', { ticket_code: ticketCode });
    }
}

// Instance globale du client API
window.operatorApiClient = new OperatorApiClient();

// Fonction de compatibilité avec l'ancien code
window.apiRequest = (endpoint, options = {}) => {
    return window.operatorApiClient.request(endpoint, options);
};
