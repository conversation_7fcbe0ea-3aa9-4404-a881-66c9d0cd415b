/**
 * Module de gestion des statistiques du tableau de bord
 * Affichage des métriques, graphiques et données analytiques
 */

class OperatorDashboardStats {
    constructor() {
        this.charts = new Map();
        this.refreshInterval = null;
        this.refreshRate = 60000; // 1 minute
    }

    /**
     * Initialise le tableau de bord
     */
    async initialize() {
        try {
            await this.loadDashboardData();
            this.setupAutoRefresh();
        } catch (error) {
            console.error('Erreur lors de l\'initialisation du tableau de bord:', error);
            operatorUIManager.showAlert('Erreur lors du chargement du tableau de bord', 'error');
        }
    }

    /**
     * Charge les données du tableau de bord
     */
    async loadDashboardData() {
        const loadingId = operatorUIManager.showLoading('#dynamicContent', 'Chargement du tableau de bord...');
        
        try {
            const data = await operatorDataCache.getDashboardData();
            
            this.updateStatsCards(data.stats || {});
            this.updateRevenueChart(data.revenue_chart || []);
            this.updateAlertsSection(data.alerts || []);
            this.updateTodayTrips(data.today_trips || []);
            
            operatorUIManager.hideLoading(loadingId);
        } catch (error) {
            operatorUIManager.hideLoading(loadingId);
            throw error;
        }
    }

    /**
     * Met à jour les cartes de statistiques
     * @param {Object} stats - Données statistiques
     */
    updateStatsCards(stats) {
        const statsMapping = {
            'totalBookingsToday': stats.bookings_today || 0,
            'activeTrips': stats.active_trips || 0,
            'revenueToday': this.formatCurrency(stats.revenue_today || 0),
            'totalPassengers': stats.passengers_today || 0
        };

        Object.keys(statsMapping).forEach(elementId => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = statsMapping[elementId];
                
                // Animation de compteur pour les nombres
                if (typeof statsMapping[elementId] === 'number') {
                    this.animateCounter(element, statsMapping[elementId]);
                }
            }
        });
    }

    /**
     * Anime un compteur numérique
     * @param {HTMLElement} element - Élément à animer
     * @param {number} targetValue - Valeur cible
     */
    animateCounter(element, targetValue) {
        const startValue = 0;
        const duration = 1000; // 1 seconde
        const startTime = Date.now();

        const updateCounter = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        };

        requestAnimationFrame(updateCounter);
    }

    /**
     * Met à jour le graphique des revenus
     * @param {Array} revenueData - Données de revenus
     */
    updateRevenueChart(revenueData) {
        const canvas = document.getElementById('revenueChart');
        if (!canvas) return;

        // Détruit le graphique existant
        if (this.charts.has('revenue')) {
            this.charts.get('revenue').destroy();
        }

        const ctx = canvas.getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: revenueData.map(item => this.formatDate(item.date)),
                datasets: [{
                    label: 'Revenus (FCFA)',
                    data: revenueData.map(item => item.amount),
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: (value) => this.formatCurrency(value)
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            }
        });

        this.charts.set('revenue', chart);
    }

    /**
     * Met à jour la section des alertes
     * @param {Array} alerts - Liste des alertes
     */
    updateAlertsSection(alerts) {
        const container = document.getElementById('alertsContainer');
        if (!container) return;

        if (!alerts || alerts.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-2x text-success"></i>
                    <p class="mt-2 text-muted">Aucune alerte</p>
                </div>
            `;
            return;
        }

        let alertsHtml = '';
        alerts.forEach(alert => {
            const iconClass = this.getAlertIcon(alert.type);
            const timeAgo = this.getTimeAgo(alert.created_at);
            
            alertsHtml += `
                <div class="alert-item">
                    <div class="d-flex align-items-start">
                        <i class="${iconClass} me-2 mt-1"></i>
                        <div class="flex-grow-1">
                            <div class="fw-bold">${alert.title}</div>
                            <div class="text-muted small">${alert.message}</div>
                            <div class="text-muted small mt-1">${timeAgo}</div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = alertsHtml;
    }

    /**
     * Met à jour la section des voyages du jour
     * @param {Array} trips - Liste des voyages
     */
    updateTodayTrips(trips) {
        const container = document.getElementById('todayTripsContainer');
        if (!container) return;

        if (!trips || trips.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-2x text-muted"></i>
                    <p class="mt-2 text-muted">Aucun voyage programmé aujourd'hui</p>
                </div>
            `;
            return;
        }

        const columns = [
            { field: 'route_name', title: 'Itinéraire' },
            { field: 'bus_registration', title: 'Bus' },
            { field: 'departure_time', title: 'Départ', render: (value) => this.formatTime(value) },
            { field: 'arrival_time', title: 'Arrivée', render: (value) => this.formatTime(value) },
            { field: 'status', title: 'Statut', render: (value) => this.formatTripStatus(value) },
            { field: 'passengers', title: 'Passagers' }
        ];

        const tableHtml = operatorUIManager.createTable(trips, columns, {
            actions: [
                {
                    icon: 'fas fa-eye',
                    title: 'Voir détails',
                    variant: 'primary',
                    onclick: 'viewTripDetails({id})'
                }
            ]
        });

        container.innerHTML = tableHtml;
    }

    /**
     * Configure l'actualisation automatique
     */
    setupAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            if (operatorNavigation.getCurrentSection() === 'dashboard') {
                this.loadDashboardData();
            }
        }, this.refreshRate);
    }

    /**
     * Arrête l'actualisation automatique
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Génère un rapport de revenus
     * @param {string} period - Période du rapport
     */
    async generateRevenueReport(period = '7days') {
        try {
            const data = await operatorApiClient.get(`operator/reports/revenue?period=${period}`);
            this.showReportModal('Rapport de revenus', this.formatRevenueReport(data));
        } catch (error) {
            operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
        }
    }

    /**
     * Génère un rapport d'occupation
     */
    async generateOccupancyReport() {
        try {
            const data = await operatorApiClient.get('operator/reports/occupancy');
            this.showReportModal('Rapport d\'occupation', this.formatOccupancyReport(data));
        } catch (error) {
            operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
        }
    }

    /**
     * Affiche une modale de rapport
     * @param {string} title - Titre du rapport
     * @param {string} content - Contenu HTML du rapport
     */
    showReportModal(title, content) {
        const modalHtml = `
            <div class="modal fade" id="reportModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            <button type="button" class="btn btn-primary" onclick="printReport()">
                                <i class="fas fa-print me-2"></i>Imprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprime la modale existante si elle existe
        const existingModal = document.getElementById('reportModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Ajoute la nouvelle modale
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Affiche la modale
        const modal = new bootstrap.Modal(document.getElementById('reportModal'));
        modal.show();
    }

    // ==================== MÉTHODES UTILITAIRES ====================

    /**
     * Formate une devise
     * @param {number} amount - Montant
     * @returns {string} - Montant formaté
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('fr-FR', {
            style: 'currency',
            currency: 'XOF',
            minimumFractionDigits: 0
        }).format(amount).replace('XOF', 'FCFA');
    }

    /**
     * Formate une date
     * @param {string} dateString - Date en string
     * @returns {string} - Date formatée
     */
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit'
        });
    }

    /**
     * Formate une heure
     * @param {string} timeString - Heure en string
     * @returns {string} - Heure formatée
     */
    formatTime(timeString) {
        return new Date(timeString).toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Formate le statut d'un voyage
     * @param {string} status - Statut
     * @returns {string} - HTML du statut formaté
     */
    formatTripStatus(status) {
        const statusConfig = {
            'planned': { class: 'bg-info', text: 'Planifié' },
            'ongoing': { class: 'bg-success', text: 'En cours' },
            'completed': { class: 'bg-secondary', text: 'Terminé' },
            'delayed': { class: 'bg-warning', text: 'Retardé' },
            'cancelled': { class: 'bg-danger', text: 'Annulé' }
        };

        const config = statusConfig[status] || { class: 'bg-secondary', text: status };
        return `<span class="badge ${config.class}">${config.text}</span>`;
    }

    /**
     * Retourne l'icône pour un type d'alerte
     * @param {string} type - Type d'alerte
     * @returns {string} - Classe CSS de l'icône
     */
    getAlertIcon(type) {
        const icons = {
            'warning': 'fas fa-exclamation-triangle text-warning',
            'error': 'fas fa-exclamation-circle text-danger',
            'info': 'fas fa-info-circle text-info',
            'success': 'fas fa-check-circle text-success'
        };
        return icons[type] || icons.info;
    }

    /**
     * Calcule le temps écoulé depuis une date
     * @param {string} dateString - Date en string
     * @returns {string} - Temps écoulé formaté
     */
    getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'À l\'instant';
        if (diffMins < 60) return `Il y a ${diffMins} min`;
        if (diffHours < 24) return `Il y a ${diffHours}h`;
        return `Il y a ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
    }

    /**
     * Nettoie les ressources
     */
    cleanup() {
        this.stopAutoRefresh();
        
        // Détruit tous les graphiques
        this.charts.forEach(chart => chart.destroy());
        this.charts.clear();
    }
}

// Instance globale des statistiques
window.operatorDashboardStats = new OperatorDashboardStats();
