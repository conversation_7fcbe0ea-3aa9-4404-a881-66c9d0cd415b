/**
 * Module de cache de données avec lazy loading
 * Évite les appels API redondants et assure la disponibilité des données
 */

class OperatorDataCache {
    constructor() {
        this.cache = new Map();
        this.loadingPromises = new Map();
        this.cacheExpiry = new Map();
        this.defaultTTL = 5 * 60 * 1000; // 5 minutes par défaut
    }

    /**
     * Génère une clé de cache
     * @param {string} type - Type de données
     * @param {Object} params - Paramètres de la requête
     * @returns {string} - Clé de cache
     */
    generateKey(type, params = {}) {
        const paramString = Object.keys(params).length > 0 
            ? JSON.stringify(params) 
            : '';
        return `${type}${paramString}`;
    }

    /**
     * Vérifie si les données en cache sont encore valides
     * @param {string} key - Clé de cache
     * @returns {boolean} - True si valide
     */
    isValid(key) {
        const expiry = this.cacheExpiry.get(key);
        return expiry && Date.now() < expiry;
    }

    /**
     * Met en cache des données avec TTL
     * @param {string} key - Clé de cache
     * @param {*} data - Données à mettre en cache
     * @param {number} ttl - Durée de vie en millisecondes
     */
    set(key, data, ttl = this.defaultTTL) {
        this.cache.set(key, data);
        this.cacheExpiry.set(key, Date.now() + ttl);
    }

    /**
     * Récupère des données du cache
     * @param {string} key - Clé de cache
     * @returns {*} - Données en cache ou null
     */
    get(key) {
        if (this.isValid(key)) {
            return this.cache.get(key);
        }
        // Nettoie les données expirées
        this.cache.delete(key);
        this.cacheExpiry.delete(key);
        return null;
    }

    /**
     * Charge des données avec lazy loading et cache
     * @param {string} type - Type de données
     * @param {Function} loader - Fonction de chargement
     * @param {Object} params - Paramètres
     * @param {number} ttl - Durée de vie du cache
     * @returns {Promise} - Promesse des données
     */
    async load(type, loader, params = {}, ttl = this.defaultTTL) {
        const key = this.generateKey(type, params);
        
        // Retourne les données en cache si valides
        const cachedData = this.get(key);
        if (cachedData) {
            return cachedData;
        }

        // Évite les requêtes multiples simultanées
        if (this.loadingPromises.has(key)) {
            return this.loadingPromises.get(key);
        }

        // Lance le chargement
        const loadingPromise = loader(params)
            .then(data => {
                this.set(key, data, ttl);
                this.loadingPromises.delete(key);
                return data;
            })
            .catch(error => {
                this.loadingPromises.delete(key);
                throw error;
            });

        this.loadingPromises.set(key, loadingPromise);
        return loadingPromise;
    }

    /**
     * Invalide le cache pour un type de données
     * @param {string} type - Type de données
     */
    invalidate(type) {
        const keysToDelete = [];
        for (const key of this.cache.keys()) {
            if (key.startsWith(type)) {
                keysToDelete.push(key);
            }
        }
        keysToDelete.forEach(key => {
            this.cache.delete(key);
            this.cacheExpiry.delete(key);
        });
    }

    /**
     * Vide tout le cache
     */
    clear() {
        this.cache.clear();
        this.cacheExpiry.clear();
        this.loadingPromises.clear();
    }

    // ==================== MÉTHODES SPÉCIFIQUES OPÉRATEUR ====================

    /**
     * Charge les lieux avec cache
     */
    async getLocations() {
        return this.load('locations', () => operatorApiClient.getLocations());
    }

    /**
     * Charge les arrêts avec cache
     */
    async getStops() {
        return this.load('stops', () => operatorApiClient.getStops());
    }

    /**
     * Charge les commodités avec cache
     */
    async getAmenities() {
        return this.load('amenities', () => operatorApiClient.getAmenities());
    }

    /**
     * Charge les plans de sièges avec cache
     */
    async getSeatPlans() {
        return this.load('seatPlans', () => operatorApiClient.get('operator/seat-plans'));
    }

    /**
     * Charge les bus avec cache
     */
    async getBuses() {
        return this.load('buses', () => operatorApiClient.getBuses());
    }

    /**
     * Charge les itinéraires avec cache
     */
    async getRoutes() {
        return this.load('routes', () => operatorApiClient.getRoutes());
    }

    /**
     * Charge les utilisateurs (chauffeurs, contrôleurs) avec cache
     */
    async getDrivers() {
        return this.load('drivers', () => operatorApiClient.get('operator/drivers'));
    }

    async getControllers() {
        return this.load('controllers', () => operatorApiClient.get('operator/controllers'));
    }

    /**
     * Charge les données du tableau de bord avec cache court
     */
    async getDashboardData() {
        return this.load('dashboard', () => operatorApiClient.getDashboardData(), {}, 60000); // 1 minute
    }

    /**
     * Charge les voyages avec cache et filtres
     */
    async getTrips(filters = {}) {
        return this.load('trips', (params) => operatorApiClient.getTrips(params), filters, 120000); // 2 minutes
    }

    /**
     * Charge les réservations avec cache et filtres
     */
    async getBookings(filters = {}) {
        return this.load('bookings', (params) => operatorApiClient.getBookings(params), filters, 60000); // 1 minute
    }

    /**
     * Charge les paiements avec cache et filtres
     */
    async getPayments(filters = {}) {
        return this.load('payments', (params) => operatorApiClient.getPayments(params), filters, 120000); // 2 minutes
    }

    /**
     * Charge les utilisateurs avec cache et filtres
     */
    async getUsers(filters = {}) {
        return this.load('users', (params) => operatorApiClient.getUsers(params), filters, 300000); // 5 minutes
    }

    /**
     * Charge la tarification avec cache
     */
    async getPricing() {
        return this.load('pricing', () => operatorApiClient.getPricing());
    }

    /**
     * Invalide le cache après une modification
     */
    invalidateAfterUpdate(type) {
        this.invalidate(type);
        
        // Invalide aussi les données liées
        const dependencies = {
            'locations': ['stops', 'routes'],
            'stops': ['routes'],
            'buses': ['trips', 'seats'],
            'routes': ['trips', 'pricing'],
            'seatPlans': ['buses', 'seats'],
            'amenities': ['buses']
        };

        if (dependencies[type]) {
            dependencies[type].forEach(dep => this.invalidate(dep));
        }
    }

    /**
     * Précharge les données essentielles
     */
    async preloadEssentialData() {
        const essentialData = [
            this.getLocations(),
            this.getRoutes(),
            this.getBuses(),
            this.getDrivers(),
            this.getControllers()
        ];

        try {
            await Promise.all(essentialData);
            console.log('Données essentielles préchargées');
        } catch (error) {
            console.error('Erreur lors du préchargement:', error);
        }
    }
}

// Instance globale du cache
window.operatorDataCache = new OperatorDataCache();

// Fonction de compatibilité
window.getCachedData = (type, params) => {
    const key = operatorDataCache.generateKey(type, params);
    return operatorDataCache.get(key);
};
