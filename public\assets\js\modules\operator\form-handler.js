/**
 * Module de gestion des formulaires
 * Gestion centralisée des formulaires avec validation et soumission
 */

class OperatorFormHandler {
    constructor() {
        this.validators = new Map();
        this.submitHandlers = new Map();
        this.setupDefaultValidators();
    }

    /**
     * Configure les validateurs par défaut
     */
    setupDefaultValidators() {
        this.validators.set('required', (value) => {
            return value && value.toString().trim() !== '';
        });

        this.validators.set('email', (value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return !value || emailRegex.test(value);
        });

        this.validators.set('phone', (value) => {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
            return !value || phoneRegex.test(value);
        });

        this.validators.set('number', (value) => {
            return !value || !isNaN(value);
        });

        this.validators.set('min', (value, min) => {
            return !value || parseFloat(value) >= parseFloat(min);
        });

        this.validators.set('max', (value, max) => {
            return !value || parseFloat(value) <= parseFloat(max);
        });
    }

    /**
     * Enregistre un gestionnaire de soumission pour un formulaire
     * @param {string} formId - ID du formulaire
     * @param {Function} handler - Fonction de gestion
     */
    registerSubmitHandler(formId, handler) {
        this.submitHandlers.set(formId, handler);
        
        // Attache l'événement de soumission
        const form = document.getElementById(formId);
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubmit(formId);
            });
        }
    }

    /**
     * Gère la soumission d'un formulaire
     * @param {string} formId - ID du formulaire
     */
    async handleSubmit(formId) {
        const form = document.getElementById(formId);
        if (!form) return;

        try {
            // Valide le formulaire
            const validation = this.validateForm(formId);
            if (!validation.isValid) {
                operatorUIManager.showFormErrors(formId, validation.errors);
                return;
            }

            // Récupère les données
            const formData = operatorUIManager.getFormData(formId);

            // Appelle le gestionnaire de soumission
            const handler = this.submitHandlers.get(formId);
            if (handler) {
                await handler(formData);
            }

        } catch (error) {
            console.error(`Erreur lors de la soumission du formulaire ${formId}:`, error);
            operatorUIManager.showAlert(`Erreur: ${error.message}`, 'error');
        }
    }

    /**
     * Valide un formulaire
     * @param {string} formId - ID du formulaire
     * @returns {Object} - Résultat de validation
     */
    validateForm(formId) {
        const form = document.getElementById(formId);
        if (!form) return { isValid: false, errors: {} };

        const errors = {};
        const fields = form.querySelectorAll('input, select, textarea');

        fields.forEach(field => {
            const fieldErrors = this.validateField(field);
            if (fieldErrors.length > 0) {
                errors[field.id] = fieldErrors[0]; // Première erreur seulement
            }
        });

        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    }

    /**
     * Valide un champ individuel
     * @param {HTMLElement} field - Élément du champ
     * @returns {Array} - Liste des erreurs
     */
    validateField(field) {
        const errors = [];
        const value = field.value;

        // Validation required
        if (field.hasAttribute('required') && !this.validators.get('required')(value)) {
            errors.push('Ce champ est obligatoire');
            return errors;
        }

        // Validation par type
        if (value) {
            switch (field.type) {
                case 'email':
                    if (!this.validators.get('email')(value)) {
                        errors.push('Format d\'email invalide');
                    }
                    break;
                case 'tel':
                    if (!this.validators.get('phone')(value)) {
                        errors.push('Format de téléphone invalide');
                    }
                    break;
                case 'number':
                    if (!this.validators.get('number')(value)) {
                        errors.push('Doit être un nombre');
                    }
                    break;
            }

            // Validation min/max pour les nombres
            if (field.type === 'number') {
                if (field.hasAttribute('min') && !this.validators.get('min')(value, field.getAttribute('min'))) {
                    errors.push(`Valeur minimale: ${field.getAttribute('min')}`);
                }
                if (field.hasAttribute('max') && !this.validators.get('max')(value, field.getAttribute('max'))) {
                    errors.push(`Valeur maximale: ${field.getAttribute('max')}`);
                }
            }
        }

        return errors;
    }

    /**
     * Remplit les sélecteurs avec des données
     * @param {string} selectId - ID du sélecteur
     * @param {Array} data - Données à afficher
     * @param {Object} options - Options de configuration
     */
    populateSelect(selectId, data, options = {}) {
        const select = document.getElementById(selectId);
        if (!select) return;

        // Sauvegarde la valeur actuelle
        const currentValue = select.value;

        // Vide le sélecteur (garde l'option par défaut si elle existe)
        const defaultOption = select.querySelector('option[value=""]');
        select.innerHTML = '';
        
        if (defaultOption) {
            select.appendChild(defaultOption);
        }

        // Ajoute les nouvelles options
        data.forEach(item => {
            const option = document.createElement('option');
            option.value = item[options.valueField || 'id'];
            option.textContent = item[options.textField || 'name'];
            
            if (options.formatter) {
                option.textContent = options.formatter(item);
            }
            
            select.appendChild(option);
        });

        // Restaure la valeur si elle existe toujours
        if (currentValue && select.querySelector(`option[value="${currentValue}"]`)) {
            select.value = currentValue;
        }
    }

    /**
     * Configure les sélecteurs dépendants
     * @param {string} parentSelectId - ID du sélecteur parent
     * @param {string} childSelectId - ID du sélecteur enfant
     * @param {Function} dataLoader - Fonction de chargement des données
     */
    setupDependentSelect(parentSelectId, childSelectId, dataLoader) {
        const parentSelect = document.getElementById(parentSelectId);
        const childSelect = document.getElementById(childSelectId);
        
        if (!parentSelect || !childSelect) return;

        parentSelect.addEventListener('change', async () => {
            const parentValue = parentSelect.value;
            
            if (!parentValue) {
                childSelect.innerHTML = '<option value="">Sélectionner...</option>';
                return;
            }

            try {
                const loadingId = operatorUIManager.showLoading(childSelect.parentElement, 'Chargement...');
                const data = await dataLoader(parentValue);
                this.populateSelect(childSelectId, data);
                operatorUIManager.hideLoading(loadingId);
            } catch (error) {
                console.error('Erreur lors du chargement des données dépendantes:', error);
                operatorUIManager.showAlert('Erreur lors du chargement', 'error');
            }
        });
    }

    // ==================== GESTIONNAIRES SPÉCIFIQUES ====================

    /**
     * Initialise les formulaires de l'opérateur
     */
    initializeOperatorForms() {
        // Formulaire de lieu
        this.registerSubmitHandler('locationForm', async (data) => {
            const isEdit = !!data.locationId;
            
            if (isEdit) {
                await operatorApiClient.updateLocation(data.locationId, data);
                operatorUIManager.showAlert('Lieu mis à jour avec succès', 'success');
            } else {
                await operatorApiClient.createLocation(data);
                operatorUIManager.showAlert('Lieu créé avec succès', 'success');
            }
            
            operatorDataCache.invalidateAfterUpdate('locations');
            operatorUIManager.hideModal('locationModal');
            
            // Recharge la page si on est sur la section locations
            if (operatorNavigation.getCurrentSection() === 'locations') {
                await window.loadLocations();
            }
        });

        // Formulaire d'arrêt
        this.registerSubmitHandler('stopForm', async (data) => {
            const isEdit = !!data.stopId;
            
            if (isEdit) {
                await operatorApiClient.updateStop(data.stopId, data);
                operatorUIManager.showAlert('Arrêt mis à jour avec succès', 'success');
            } else {
                await operatorApiClient.createStop(data);
                operatorUIManager.showAlert('Arrêt créé avec succès', 'success');
            }
            
            operatorDataCache.invalidateAfterUpdate('stops');
            operatorUIManager.hideModal('stopModal');
            
            if (operatorNavigation.getCurrentSection() === 'stops') {
                await window.loadStops();
            }
        });

        // Formulaire de commodité
        this.registerSubmitHandler('amenityForm', async (data) => {
            const isEdit = !!data.amenityId;
            
            if (isEdit) {
                await operatorApiClient.updateAmenity(data.amenityId, data);
                operatorUIManager.showAlert('Commodité mise à jour avec succès', 'success');
            } else {
                await operatorApiClient.createAmenity(data);
                operatorUIManager.showAlert('Commodité créée avec succès', 'success');
            }
            
            operatorDataCache.invalidateAfterUpdate('amenities');
            operatorUIManager.hideModal('amenityModal');
            
            if (operatorNavigation.getCurrentSection() === 'amenities') {
                await window.loadAmenities();
            }
        });

        // Autres formulaires...
        this.setupFormDependencies();
    }

    /**
     * Configure les dépendances entre formulaires
     */
    setupFormDependencies() {
        // Arrêts dépendent des lieux
        this.setupDependentSelect('stopLocationId', 'stopLocationId', async (locationId) => {
            // Retourne les arrêts pour un lieu donné si nécessaire
            return [];
        });

        // Voyages dépendent des itinéraires pour les bus disponibles
        this.setupDependentSelect('tripRouteId', 'tripBusId', async (routeId) => {
            const buses = await operatorDataCache.getBuses();
            return buses.filter(bus => bus.status === 'active');
        });
    }

    /**
     * Prépare un formulaire pour l'édition
     * @param {string} formId - ID du formulaire
     * @param {Object} data - Données à charger
     */
    async prepareFormForEdit(formId, data) {
        // Charge les données dépendantes si nécessaire
        await this.loadFormDependencies(formId);
        
        // Remplit le formulaire
        operatorUIManager.populateForm(formId, data);
    }

    /**
     * Charge les dépendances d'un formulaire
     * @param {string} formId - ID du formulaire
     */
    async loadFormDependencies(formId) {
        switch (formId) {
            case 'stopForm':
                const locations = await operatorDataCache.getLocations();
                this.populateSelect('stopLocationId', locations, { textField: 'name' });
                break;
                
            case 'routeForm':
                const routeLocations = await operatorDataCache.getLocations();
                this.populateSelect('routeDepartureLocationId', routeLocations, { textField: 'name' });
                this.populateSelect('routeDestinationLocationId', routeLocations, { textField: 'name' });
                break;
                
            case 'tripForm':
                const [routes, buses, drivers, controllers] = await Promise.all([
                    operatorDataCache.getRoutes(),
                    operatorDataCache.getBuses(),
                    operatorDataCache.getDrivers(),
                    operatorDataCache.getControllers()
                ]);
                
                this.populateSelect('tripRouteId', routes, { textField: 'name' });
                this.populateSelect('tripBusId', buses, { 
                    textField: 'registration_number',
                    formatter: (bus) => `${bus.registration_number} (${bus.capacity} places)`
                });
                this.populateSelect('tripDriverId', drivers, {
                    textField: 'name',
                    formatter: (driver) => `${driver.first_name} ${driver.last_name}`
                });
                this.populateSelect('tripControllerId', controllers, {
                    textField: 'name',
                    formatter: (controller) => `${controller.first_name} ${controller.last_name}`
                });
                break;
        }
    }
}

// Instance globale du gestionnaire de formulaires
window.operatorFormHandler = new OperatorFormHandler();
