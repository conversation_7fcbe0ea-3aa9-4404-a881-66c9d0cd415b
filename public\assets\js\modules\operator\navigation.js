/**
 * Module de navigation pour le chargement dynamique des pages
 * Gestion du routage et du chargement des contenus
 */

class OperatorNavigation {
    constructor() {
        this.currentSection = 'dashboard';
        this.pageCache = new Map();
        this.contentContainer = document.getElementById('dynamicContent');
        this.basePath = '/operator/pages/';
        
        // Configuration des pages
        this.pages = {
            dashboard: {
                file: 'dashboard.html',
                title: 'Tableau de bord',
                requiresData: ['dashboard']
            },
            locations: {
                file: 'locations.html',
                title: 'Gestion des Lieux/Villes',
                requiresData: ['locations']
            },
            stops: {
                file: 'stops.html',
                title: 'Gestion des Arrêts',
                requiresData: ['stops', 'locations']
            },
            amenities: {
                file: 'amenities.html',
                title: 'Gestion des Commodités',
                requiresData: ['amenities']
            },
            'seatPlans': {
                file: 'seat-plans.html',
                title: 'Gestion des Plans de Sièges',
                requiresData: ['seatPlans']
            },
            buses: {
                file: 'buses.html',
                title: 'Gestion des Bus',
                requiresData: ['buses', 'seatPlans']
            },
            seats: {
                file: 'seats.html',
                title: 'Gestion des Sièges',
                requiresData: ['buses', 'seats']
            },
            routes: {
                file: 'routes.html',
                title: 'Gestion des Itinéraires',
                requiresData: ['routes', 'locations']
            },
            trips: {
                file: 'trips.html',
                title: 'Gestion des Voyages',
                requiresData: ['trips', 'routes', 'buses', 'drivers', 'controllers']
            },
            pricing: {
                file: 'pricing.html',
                title: 'Gestion de la Tarification',
                requiresData: ['pricing', 'routes']
            },
            bookings: {
                file: 'bookings.html',
                title: 'Gestion des Réservations',
                requiresData: ['bookings']
            },
            payments: {
                file: 'payments.html',
                title: 'Suivi des Paiements',
                requiresData: ['payments']
            },
            users: {
                file: 'users.html',
                title: 'Gestion des Utilisateurs',
                requiresData: ['users']
            },
            validation: {
                file: 'validation.html',
                title: 'Validation des Tickets',
                requiresData: []
            },
            reports: {
                file: 'reports.html',
                title: 'Rapports et Analyses',
                requiresData: []
            }
        };
    }

    /**
     * Navigue vers une section
     * @param {string} sectionName - Nom de la section
     * @param {Object} options - Options de navigation
     */
    async navigateTo(sectionName, options = {}) {
        if (!this.pages[sectionName]) {
            console.error(`Section ${sectionName} non trouvée`);
            return;
        }

        try {
            // Affiche le chargement
            const loadingId = operatorUIManager.showLoading(this.contentContainer, 'Chargement de la page...');

            // Met à jour l'interface
            operatorUIManager.updatePageTitle(this.pages[sectionName].title);
            operatorUIManager.updateActiveNavigation(sectionName);

            // Charge le contenu de la page
            const content = await this.loadPageContent(sectionName);
            
            // Précharge les données nécessaires
            await this.preloadPageData(sectionName);

            // Affiche le contenu
            this.contentContainer.innerHTML = content;

            // Initialise la page
            await this.initializePage(sectionName);

            // Met à jour la section courante
            this.currentSection = sectionName;

            operatorUIManager.hideLoading(loadingId);

        } catch (error) {
            console.error(`Erreur lors de la navigation vers ${sectionName}:`, error);
            operatorUIManager.showAlert(`Erreur lors du chargement de la page: ${error.message}`, 'error');
        }
    }

    /**
     * Charge le contenu HTML d'une page
     * @param {string} sectionName - Nom de la section
     * @returns {Promise<string>} - Contenu HTML
     */
    async loadPageContent(sectionName) {
        const pageConfig = this.pages[sectionName];
        
        // Vérifie le cache
        if (this.pageCache.has(sectionName)) {
            return this.pageCache.get(sectionName);
        }

        try {
            const response = await fetch(`${this.basePath}${pageConfig.file}`);
            
            if (!response.ok) {
                throw new Error(`Erreur HTTP ${response.status}`);
            }

            const content = await response.text();
            
            // Met en cache le contenu
            this.pageCache.set(sectionName, content);
            
            return content;
        } catch (error) {
            console.error(`Erreur lors du chargement de ${pageConfig.file}:`, error);
            return this.getErrorPageContent(error.message);
        }
    }

    /**
     * Précharge les données nécessaires pour une page
     * @param {string} sectionName - Nom de la section
     */
    async preloadPageData(sectionName) {
        const pageConfig = this.pages[sectionName];
        
        if (!pageConfig.requiresData || pageConfig.requiresData.length === 0) {
            return;
        }

        const dataPromises = pageConfig.requiresData.map(dataType => {
            switch (dataType) {
                case 'dashboard':
                    return operatorDataCache.getDashboardData();
                case 'locations':
                    return operatorDataCache.getLocations();
                case 'stops':
                    return operatorDataCache.getStops();
                case 'amenities':
                    return operatorDataCache.getAmenities();
                case 'seatPlans':
                    return operatorDataCache.getSeatPlans();
                case 'buses':
                    return operatorDataCache.getBuses();
                case 'routes':
                    return operatorDataCache.getRoutes();
                case 'drivers':
                    return operatorDataCache.getDrivers();
                case 'controllers':
                    return operatorDataCache.getControllers();
                case 'trips':
                    return operatorDataCache.getTrips();
                case 'bookings':
                    return operatorDataCache.getBookings();
                case 'payments':
                    return operatorDataCache.getPayments();
                case 'users':
                    return operatorDataCache.getUsers();
                case 'pricing':
                    return operatorDataCache.getPricing();
                default:
                    return Promise.resolve(null);
            }
        });

        try {
            await Promise.all(dataPromises);
        } catch (error) {
            console.warn(`Erreur lors du préchargement des données pour ${sectionName}:`, error);
        }
    }

    /**
     * Initialise une page après son chargement
     * @param {string} sectionName - Nom de la section
     */
    async initializePage(sectionName) {
        // Appelle la fonction d'initialisation spécifique à la page si elle existe
        const initFunctionName = `init${sectionName.charAt(0).toUpperCase() + sectionName.slice(1)}Page`;
        
        if (typeof window[initFunctionName] === 'function') {
            try {
                await window[initFunctionName]();
            } catch (error) {
                console.error(`Erreur lors de l'initialisation de ${sectionName}:`, error);
            }
        }

        // Initialise les composants Bootstrap dans la nouvelle page
        this.initializeBootstrapComponents();
    }

    /**
     * Initialise les composants Bootstrap
     */
    initializeBootstrapComponents() {
        // Initialise les tooltips
        const tooltipTriggerList = [].slice.call(this.contentContainer.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialise les popovers
        const popoverTriggerList = [].slice.call(this.contentContainer.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }

    /**
     * Retourne le contenu d'une page d'erreur
     * @param {string} errorMessage - Message d'erreur
     * @returns {string} - HTML de la page d'erreur
     */
    getErrorPageContent(errorMessage) {
        return `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h4>Erreur de chargement</h4>
                <p class="text-muted">${errorMessage}</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-2"></i>Recharger la page
                </button>
            </div>
        `;
    }

    /**
     * Rafraîchit la page courante
     */
    async refresh() {
        // Vide le cache de la page courante
        this.pageCache.delete(this.currentSection);
        
        // Invalide le cache des données
        operatorDataCache.clear();
        
        // Recharge la page
        await this.navigateTo(this.currentSection);
    }

    /**
     * Retourne à la page précédente ou au tableau de bord
     */
    goBack() {
        // Pour l'instant, retourne toujours au tableau de bord
        // Peut être étendu pour gérer un historique de navigation
        this.navigateTo('dashboard');
    }

    /**
     * Vérifie si une section existe
     * @param {string} sectionName - Nom de la section
     * @returns {boolean} - True si la section existe
     */
    sectionExists(sectionName) {
        return this.pages.hasOwnProperty(sectionName);
    }

    /**
     * Retourne la section courante
     * @returns {string} - Nom de la section courante
     */
    getCurrentSection() {
        return this.currentSection;
    }
}

// Instance globale de navigation
window.operatorNavigation = new OperatorNavigation();

// Fonction de compatibilité avec l'ancien code
window.showSection = (sectionName) => {
    operatorNavigation.navigateTo(sectionName);
};

// Fonction pour rafraîchir la page courante
window.refreshCurrentPage = () => {
    operatorNavigation.refresh();
};
