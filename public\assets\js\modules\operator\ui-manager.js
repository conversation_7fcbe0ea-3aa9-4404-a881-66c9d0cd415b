/**
 * Module de gestion de l'interface utilisateur
 * Gestion des éléments DOM, modales, alertes et états de chargement
 */

class OperatorUIManager {
    constructor() {
        this.loadingStates = new Map();
        this.alertContainer = document.getElementById('alertContainer');
    }

    /**
     * Affiche un indicateur de chargement
     * @param {string|HTMLElement} target - Sélecteur ou élément cible
     * @param {string} message - Message de chargement
     */
    showLoading(target, message = 'Chargement...') {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;

        const loadingId = this.generateLoadingId();
        this.loadingStates.set(loadingId, element);

        element.innerHTML = `
            <div class="text-center py-4" data-loading-id="${loadingId}">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">${message}</p>
            </div>
        `;

        return loadingId;
    }

    /**
     * Masque l'indicateur de chargement
     * @param {string} loadingId - ID de chargement
     */
    hideLoading(loadingId) {
        if (!this.loadingStates.has(loadingId)) return;

        const element = this.loadingStates.get(loadingId);
        const loadingElement = element.querySelector(`[data-loading-id="${loadingId}"]`);
        
        if (loadingElement) {
            loadingElement.remove();
        }

        this.loadingStates.delete(loadingId);
    }

    /**
     * Génère un ID unique pour le chargement
     */
    generateLoadingId() {
        return 'loading_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Affiche une alerte
     * @param {string} message - Message de l'alerte
     * @param {string} type - Type d'alerte (success, error, warning, info)
     * @param {number} duration - Durée d'affichage en ms
     */
    showAlert(message, type = 'info', duration = 5000) {
        const alertId = 'alert_' + Date.now();
        const alertClass = this.getAlertClass(type);
        const iconClass = this.getAlertIcon(type);

        const alertHtml = `
            <div id="${alertId}" class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        this.alertContainer.insertAdjacentHTML('beforeend', alertHtml);

        // Auto-suppression après la durée spécifiée
        if (duration > 0) {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, duration);
        }
    }

    /**
     * Retourne la classe CSS pour le type d'alerte
     */
    getAlertClass(type) {
        const classes = {
            success: 'alert-success',
            error: 'alert-danger',
            warning: 'alert-warning',
            info: 'alert-info'
        };
        return classes[type] || classes.info;
    }

    /**
     * Retourne l'icône pour le type d'alerte
     */
    getAlertIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }

    /**
     * Affiche une modale
     * @param {string} modalId - ID de la modale
     * @param {Object} options - Options de la modale
     */
    showModal(modalId, options = {}) {
        const modalElement = document.getElementById(modalId);
        if (!modalElement) {
            console.error(`Modale ${modalId} non trouvée`);
            return;
        }

        // Configure le titre si fourni
        if (options.title) {
            const titleElement = modalElement.querySelector('.modal-title');
            if (titleElement) {
                titleElement.textContent = options.title;
            }
        }

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        return modal;
    }

    /**
     * Masque une modale
     * @param {string} modalId - ID de la modale
     */
    hideModal(modalId) {
        const modalElement = document.getElementById(modalId);
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }
    }

    /**
     * Remplit un formulaire avec des données
     * @param {string} formId - ID du formulaire
     * @param {Object} data - Données à remplir
     */
    populateForm(formId, data) {
        const form = document.getElementById(formId);
        if (!form) return;

        Object.keys(data).forEach(key => {
            const field = form.querySelector(`#${key}`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = Boolean(data[key]);
                } else if (field.type === 'radio') {
                    const radio = form.querySelector(`input[name="${key}"][value="${data[key]}"]`);
                    if (radio) radio.checked = true;
                } else {
                    field.value = data[key] || '';
                }
            }
        });
    }

    /**
     * Récupère les données d'un formulaire
     * @param {string} formId - ID du formulaire
     * @returns {Object} - Données du formulaire
     */
    getFormData(formId) {
        const form = document.getElementById(formId);
        if (!form) return {};

        const formData = new FormData(form);
        const data = {};

        // Récupère tous les champs du formulaire
        const fields = form.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            if (field.id) {
                if (field.type === 'checkbox') {
                    data[field.id] = field.checked;
                } else if (field.type === 'radio') {
                    if (field.checked) {
                        data[field.name] = field.value;
                    }
                } else {
                    data[field.id] = field.value;
                }
            }
        });

        return data;
    }

    /**
     * Réinitialise un formulaire
     * @param {string} formId - ID du formulaire
     */
    resetForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            // Supprime les classes de validation
            form.querySelectorAll('.is-valid, .is-invalid').forEach(field => {
                field.classList.remove('is-valid', 'is-invalid');
            });
        }
    }

    /**
     * Valide un formulaire et affiche les erreurs
     * @param {string} formId - ID du formulaire
     * @param {Object} errors - Erreurs de validation
     */
    showFormErrors(formId, errors) {
        const form = document.getElementById(formId);
        if (!form) return;

        // Supprime les erreurs précédentes
        form.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(feedback => {
            feedback.remove();
        });

        // Affiche les nouvelles erreurs
        Object.keys(errors).forEach(fieldName => {
            const field = form.querySelector(`#${fieldName}`);
            if (field) {
                field.classList.add('is-invalid');
                
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = errors[fieldName];
                
                field.parentNode.appendChild(feedback);
            }
        });
    }

    /**
     * Met à jour le titre de la page
     * @param {string} title - Nouveau titre
     */
    updatePageTitle(title) {
        const titleElement = document.getElementById('pageTitle');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    /**
     * Met à jour la navigation active
     * @param {string} sectionName - Nom de la section active
     */
    updateActiveNavigation(sectionName) {
        // Supprime la classe active de tous les liens
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Ajoute la classe active au lien correspondant
        const activeLink = document.querySelector(`.sidebar .nav-link[onclick*="${sectionName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    /**
     * Crée un tableau HTML à partir de données
     * @param {Array} data - Données du tableau
     * @param {Array} columns - Configuration des colonnes
     * @param {Object} options - Options du tableau
     * @returns {string} - HTML du tableau
     */
    createTable(data, columns, options = {}) {
        if (!data || data.length === 0) {
            return `
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-2x text-muted"></i>
                    <p class="mt-2 text-muted">Aucune donnée disponible</p>
                </div>
            `;
        }

        let tableHtml = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
        `;

        // En-têtes de colonnes
        columns.forEach(column => {
            tableHtml += `<th>${column.title}</th>`;
        });

        if (options.actions) {
            tableHtml += `<th>Actions</th>`;
        }

        tableHtml += `
                        </tr>
                    </thead>
                    <tbody>
        `;

        // Lignes de données
        data.forEach(row => {
            tableHtml += `<tr>`;
            
            columns.forEach(column => {
                let value = row[column.field];
                
                if (column.render) {
                    value = column.render(value, row);
                }
                
                tableHtml += `<td>${value || '-'}</td>`;
            });

            if (options.actions) {
                tableHtml += `<td>${this.createActionButtons(row, options.actions)}</td>`;
            }

            tableHtml += `</tr>`;
        });

        tableHtml += `
                    </tbody>
                </table>
            </div>
        `;

        return tableHtml;
    }

    /**
     * Crée les boutons d'action pour une ligne de tableau
     */
    createActionButtons(row, actions) {
        let buttonsHtml = `<div class="btn-group btn-group-sm">`;
        
        actions.forEach(action => {
            const onclick = action.onclick.replace('{id}', row.id);
            buttonsHtml += `
                <button type="button" class="btn btn-outline-${action.variant || 'primary'}" 
                        onclick="${onclick}" title="${action.title}">
                    <i class="${action.icon}"></i>
                </button>
            `;
        });
        
        buttonsHtml += `</div>`;
        return buttonsHtml;
    }
}

// Instance globale du gestionnaire UI
window.operatorUIManager = new OperatorUIManager();
