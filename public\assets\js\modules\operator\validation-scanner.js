/**
 * Module de validation et scan des tickets
 * Gestion de la validation des tickets avec scanner QR
 */

class OperatorValidationScanner {
    constructor() {
        this.isScanning = false;
        this.stream = null;
        this.validationHistory = [];
    }

    /**
     * Initialise le module de validation
     */
    initialize() {
        this.setupEventListeners();
        this.loadValidationHistory();
    }

    /**
     * Configure les écouteurs d'événements
     */
    setupEventListeners() {
        // Écouteur pour la saisie du code de ticket
        const ticketInput = document.getElementById('ticketCodeInput');
        if (ticketInput) {
            ticketInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.validateTicket();
                }
            });

            // Auto-focus sur le champ
            ticketInput.focus();
        }
    }

    /**
     * Valide un ticket
     * @param {string} ticketCode - Code du ticket (optionnel)
     */
    async validateTicket(ticketCode = null) {
        const input = document.getElementById('ticketCodeInput');
        const code = ticketCode || (input ? input.value.trim() : '');

        if (!code) {
            operatorUIManager.showAlert('Veuillez saisir ou scanner un code de ticket', 'warning');
            return;
        }

        const resultContainer = document.getElementById('validationResult');
        if (resultContainer) {
            const loadingId = operatorUIManager.showLoading(resultContainer, 'Validation en cours...');
            
            try {
                const result = await operatorApiClient.validateTicket(code);
                
                operatorUIManager.hideLoading(loadingId);
                this.displayValidationResult(result);
                this.addToHistory(result);
                
                // Vide le champ de saisie
                if (input) {
                    input.value = '';
                    input.focus();
                }

            } catch (error) {
                operatorUIManager.hideLoading(loadingId);
                this.displayValidationError(error.message);
            }
        }
    }

    /**
     * Affiche le résultat de validation
     * @param {Object} result - Résultat de la validation
     */
    displayValidationResult(result) {
        const container = document.getElementById('validationResult');
        if (!container) return;

        const isValid = result.status === 'valid';
        const alertClass = isValid ? 'alert-success' : 'alert-danger';
        const iconClass = isValid ? 'fas fa-check-circle' : 'fas fa-times-circle';

        let resultHtml = `
            <div class="alert ${alertClass}">
                <div class="d-flex align-items-center mb-3">
                    <i class="${iconClass} fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">${isValid ? 'Ticket Valide' : 'Ticket Invalide'}</h5>
                        <p class="mb-0">${result.message}</p>
                    </div>
                </div>
        `;

        if (isValid && result.ticket) {
            const ticket = result.ticket;
            resultHtml += `
                <div class="row">
                    <div class="col-md-6">
                        <strong>Passager:</strong> ${ticket.passenger_name}<br>
                        <strong>Téléphone:</strong> ${ticket.passenger_phone}<br>
                        <strong>Siège:</strong> ${ticket.seat_number}
                    </div>
                    <div class="col-md-6">
                        <strong>Voyage:</strong> ${ticket.route_name}<br>
                        <strong>Départ:</strong> ${this.formatDateTime(ticket.departure_time)}<br>
                        <strong>Bus:</strong> ${ticket.bus_registration}
                    </div>
                </div>
            `;
        }

        resultHtml += '</div>';

        container.innerHTML = resultHtml;

        // Auto-masquage après 5 secondes pour les tickets valides
        if (isValid) {
            setTimeout(() => {
                container.innerHTML = '';
            }, 5000);
        }
    }

    /**
     * Affiche une erreur de validation
     * @param {string} errorMessage - Message d'erreur
     */
    displayValidationError(errorMessage) {
        const container = document.getElementById('validationResult');
        if (!container) return;

        container.innerHTML = `
            <div class="alert alert-danger">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-1">Erreur de validation</h5>
                        <p class="mb-0">${errorMessage}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Démarre le scanner QR
     */
    async startQRScanner() {
        if (this.isScanning) {
            this.stopQRScanner();
            return;
        }

        try {
            // Vérifie la disponibilité de la caméra
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('Scanner non supporté par ce navigateur');
            }

            // Demande l'accès à la caméra
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' } // Caméra arrière si disponible
            });

            this.isScanning = true;
            this.showScannerModal();
            this.initializeQRReader();

        } catch (error) {
            console.error('Erreur lors du démarrage du scanner:', error);
            operatorUIManager.showAlert(`Erreur scanner: ${error.message}`, 'error');
        }
    }

    /**
     * Arrête le scanner QR
     */
    stopQRScanner() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        this.isScanning = false;
        this.hideScannerModal();
    }

    /**
     * Affiche la modale du scanner
     */
    showScannerModal() {
        const modalHtml = `
            <div class="modal fade" id="qrScannerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Scanner QR Code</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" onclick="operatorValidationScanner.stopQRScanner()"></button>
                        </div>
                        <div class="modal-body text-center">
                            <video id="qrVideo" width="100%" height="300" autoplay></video>
                            <p class="mt-3 text-muted">Positionnez le QR code dans le cadre</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="operatorValidationScanner.stopQRScanner()">
                                Fermer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprime la modale existante
        const existingModal = document.getElementById('qrScannerModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        const modal = new bootstrap.Modal(document.getElementById('qrScannerModal'));
        modal.show();
    }

    /**
     * Masque la modale du scanner
     */
    hideScannerModal() {
        const modal = document.getElementById('qrScannerModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
            modal.remove();
        }
    }

    /**
     * Initialise le lecteur QR
     */
    initializeQRReader() {
        const video = document.getElementById('qrVideo');
        if (!video || !this.stream) return;

        video.srcObject = this.stream;

        // Simulation de lecture QR (remplacer par une vraie bibliothèque QR)
        // Pour une implémentation réelle, utiliser une bibliothèque comme jsQR ou QuaggaJS
        video.addEventListener('loadedmetadata', () => {
            // Ici, vous intégreriez une vraie bibliothèque de lecture QR
            console.log('Scanner QR initialisé');
        });
    }

    /**
     * Ajoute une validation à l'historique
     * @param {Object} result - Résultat de validation
     */
    addToHistory(result) {
        const historyItem = {
            timestamp: new Date().toISOString(),
            code: result.ticket_code,
            status: result.status,
            passenger: result.ticket ? result.ticket.passenger_name : null,
            route: result.ticket ? result.ticket.route_name : null
        };

        this.validationHistory.unshift(historyItem);
        
        // Limite l'historique à 50 éléments
        if (this.validationHistory.length > 50) {
            this.validationHistory = this.validationHistory.slice(0, 50);
        }

        // Sauvegarde en localStorage
        localStorage.setItem('validationHistory', JSON.stringify(this.validationHistory));
    }

    /**
     * Charge l'historique de validation
     */
    loadValidationHistory() {
        const saved = localStorage.getItem('validationHistory');
        if (saved) {
            try {
                this.validationHistory = JSON.parse(saved);
            } catch (error) {
                console.error('Erreur lors du chargement de l\'historique:', error);
                this.validationHistory = [];
            }
        }
    }

    /**
     * Affiche l'historique de validation
     */
    showValidationHistory() {
        if (this.validationHistory.length === 0) {
            operatorUIManager.showAlert('Aucun historique de validation', 'info');
            return;
        }

        const columns = [
            { field: 'timestamp', title: 'Date/Heure', render: (value) => this.formatDateTime(value) },
            { field: 'code', title: 'Code Ticket' },
            { field: 'status', title: 'Statut', render: (value) => this.formatValidationStatus(value) },
            { field: 'passenger', title: 'Passager' },
            { field: 'route', title: 'Itinéraire' }
        ];

        const tableHtml = operatorUIManager.createTable(this.validationHistory, columns);

        const modalHtml = `
            <div class="modal fade" id="historyModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Historique de validation</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${tableHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-danger" onclick="operatorValidationScanner.clearHistory()">
                                Vider l'historique
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                Fermer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprime la modale existante
        const existingModal = document.getElementById('historyModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }

    /**
     * Vide l'historique de validation
     */
    clearHistory() {
        this.validationHistory = [];
        localStorage.removeItem('validationHistory');
        
        const modal = document.getElementById('historyModal');
        if (modal) {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        }

        operatorUIManager.showAlert('Historique vidé', 'success');
    }

    /**
     * Formate le statut de validation
     * @param {string} status - Statut
     * @returns {string} - HTML du statut formaté
     */
    formatValidationStatus(status) {
        const config = {
            'valid': { class: 'bg-success', text: 'Valide' },
            'invalid': { class: 'bg-danger', text: 'Invalide' },
            'expired': { class: 'bg-warning', text: 'Expiré' },
            'used': { class: 'bg-secondary', text: 'Déjà utilisé' }
        };

        const statusConfig = config[status] || { class: 'bg-secondary', text: status };
        return `<span class="badge ${statusConfig.class}">${statusConfig.text}</span>`;
    }

    /**
     * Formate une date/heure
     * @param {string} dateString - Date en string
     * @returns {string} - Date formatée
     */
    formatDateTime(dateString) {
        return new Date(dateString).toLocaleString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Nettoie les ressources
     */
    cleanup() {
        this.stopQRScanner();
    }
}

// Instance globale du scanner de validation
window.operatorValidationScanner = new OperatorValidationScanner();

// Fonctions de compatibilité avec l'ancien code
window.validateTicket = () => operatorValidationScanner.validateTicket();
window.startQRScanner = () => operatorValidationScanner.startQRScanner();
window.showValidationHistory = () => operatorValidationScanner.showValidationHistory();
