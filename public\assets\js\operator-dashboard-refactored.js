/**
 * Script principal du tableau de bord opérateur refactorisé
 * Orchestration des modules et initialisation de l'application
 */

class OperatorDashboardApp {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
    }

    /**
     * Initialise l'application
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            // Vérifie l'authentification
            await this.checkAuthentication();

            // Initialise les modules
            await this.initializeModules();

            // Précharge les données essentielles
            await this.preloadEssentialData();

            // Configure l'interface
            this.setupUI();

            // Charge la page par défaut
            await this.loadDefaultPage();

            this.isInitialized = true;
            console.log('Application opérateur initialisée avec succès');

        } catch (error) {
            console.error('Erreur lors de l\'initialisation:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Vérifie l'authentification de l'utilisateur
     */
    async checkAuthentication() {
        const token = getAuthToken();
        if (!token) {
            window.location.href = '/login.html';
            return;
        }

        try {
            // Vérifie la validité du token et récupère les infos utilisateur
            const userInfo = await operatorApiClient.get('auth/me');
            this.currentUser = userInfo;
            
            // Met à jour l'interface avec les infos utilisateur
            this.updateUserInfo(userInfo);
            
        } catch (error) {
            console.error('Erreur d\'authentification:', error);
            // Token invalide, redirection vers login
            localStorage.removeItem('authToken');
            window.location.href = '/login.html';
        }
    }

    /**
     * Initialise tous les modules
     */
    async initializeModules() {
        // Initialise le gestionnaire de formulaires
        operatorFormHandler.initializeOperatorForms();

        // Initialise le scanner de validation
        operatorValidationScanner.initialize();

        // Configure les gestionnaires d'événements globaux
        this.setupGlobalEventHandlers();
    }

    /**
     * Précharge les données essentielles
     */
    async preloadEssentialData() {
        try {
            await operatorDataCache.preloadEssentialData();
        } catch (error) {
            console.warn('Erreur lors du préchargement des données:', error);
            // Continue l'initialisation même si le préchargement échoue
        }
    }

    /**
     * Configure l'interface utilisateur
     */
    setupUI() {
        // Configure la navigation
        this.setupNavigation();

        // Configure les raccourcis clavier
        this.setupKeyboardShortcuts();

        // Configure la gestion des erreurs globales
        this.setupErrorHandling();
    }

    /**
     * Configure la navigation
     */
    setupNavigation() {
        // Gestionnaire pour les liens de navigation
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const onclick = link.getAttribute('onclick');
                if (onclick) {
                    // Extrait le nom de la section du onclick
                    const match = onclick.match(/showSection\('([^']+)'\)/);
                    if (match) {
                        operatorNavigation.navigateTo(match[1]);
                    }
                }
            });
        });

        // Gestionnaire pour le bouton de déconnexion
        document.querySelectorAll('[onclick="logout()"]').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.logout();
            });
        });
    }

    /**
     * Configure les raccourcis clavier
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + R : Rafraîchir la page courante
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                operatorNavigation.refresh();
            }

            // Échap : Fermer les modales ouvertes
            if (e.key === 'Escape') {
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                });
            }

            // F1 : Aide (peut être étendu)
            if (e.key === 'F1') {
                e.preventDefault();
                this.showHelp();
            }
        });
    }

    /**
     * Configure la gestion des erreurs globales
     */
    setupErrorHandling() {
        // Gestionnaire d'erreurs JavaScript globales
        window.addEventListener('error', (e) => {
            console.error('Erreur JavaScript:', e.error);
            operatorUIManager.showAlert('Une erreur inattendue s\'est produite', 'error');
        });

        // Gestionnaire d'erreurs de promesses non gérées
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Promesse rejetée non gérée:', e.reason);
            operatorUIManager.showAlert('Erreur de communication avec le serveur', 'error');
        });
    }

    /**
     * Configure les gestionnaires d'événements globaux
     */
    setupGlobalEventHandlers() {
        // Gestionnaire pour les formulaires modaux
        document.addEventListener('shown.bs.modal', (e) => {
            const modal = e.target;
            const form = modal.querySelector('form');
            if (form) {
                // Auto-focus sur le premier champ
                const firstInput = form.querySelector('input:not([type="hidden"]), select, textarea');
                if (firstInput) {
                    setTimeout(() => firstInput.focus(), 100);
                }
            }
        });

        // Gestionnaire pour la fermeture des modales
        document.addEventListener('hidden.bs.modal', (e) => {
            const modal = e.target;
            const form = modal.querySelector('form');
            if (form) {
                operatorUIManager.resetForm(form.id);
            }
        });
    }

    /**
     * Charge la page par défaut
     */
    async loadDefaultPage() {
        await operatorNavigation.navigateTo('dashboard');
    }

    /**
     * Met à jour les informations utilisateur dans l'interface
     * @param {Object} userInfo - Informations utilisateur
     */
    updateUserInfo(userInfo) {
        const operatorNameElement = document.getElementById('operatorName');
        if (operatorNameElement && userInfo.first_name) {
            operatorNameElement.textContent = `${userInfo.first_name} ${userInfo.last_name || ''}`.trim();
        }
    }

    /**
     * Gère les erreurs d'initialisation
     * @param {Error} error - Erreur d'initialisation
     */
    handleInitializationError(error) {
        const errorMessage = `
            <div class="container-fluid h-100 d-flex align-items-center justify-content-center">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h3>Erreur d'initialisation</h3>
                    <p class="text-muted mb-4">${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-2"></i>Recharger
                    </button>
                </div>
            </div>
        `;

        document.body.innerHTML = errorMessage;
    }

    /**
     * Déconnecte l'utilisateur
     */
    async logout() {
        try {
            // Nettoie les ressources
            this.cleanup();

            // Supprime le token
            localStorage.removeItem('authToken');

            // Appelle l'API de déconnexion si disponible
            try {
                await operatorApiClient.post('auth/logout');
            } catch (error) {
                // Ignore les erreurs de déconnexion API
                console.warn('Erreur lors de la déconnexion API:', error);
            }

            // Redirection vers la page de connexion
            window.location.href = '/login.html';

        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
            // Force la redirection même en cas d'erreur
            window.location.href = '/login.html';
        }
    }

    /**
     * Affiche l'aide
     */
    showHelp() {
        const helpContent = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Raccourcis clavier</h6>
                    <ul class="list-unstyled">
                        <li><kbd>Ctrl+R</kbd> - Rafraîchir la page</li>
                        <li><kbd>Échap</kbd> - Fermer les modales</li>
                        <li><kbd>F1</kbd> - Afficher cette aide</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Navigation</h6>
                    <ul class="list-unstyled">
                        <li>Utilisez le menu latéral pour naviguer</li>
                        <li>Les données sont mises en cache automatiquement</li>
                        <li>Les formulaires se valident automatiquement</li>
                    </ul>
                </div>
            </div>
        `;

        const modalHtml = `
            <div class="modal fade" id="helpModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Aide</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${helpContent}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprime la modale existante
        const existingModal = document.getElementById('helpModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        const modal = new bootstrap.Modal(document.getElementById('helpModal'));
        modal.show();
    }

    /**
     * Nettoie les ressources avant fermeture
     */
    cleanup() {
        // Nettoie les modules
        operatorDashboardStats.cleanup();
        operatorValidationScanner.cleanup();

        // Vide les caches
        operatorDataCache.clear();

        // Supprime les gestionnaires d'événements
        document.removeEventListener('keydown', this.keydownHandler);
    }

    /**
     * Retourne l'état de l'application
     */
    getAppState() {
        return {
            isInitialized: this.isInitialized,
            currentUser: this.currentUser,
            currentSection: operatorNavigation.getCurrentSection()
        };
    }
}

// Instance globale de l'application
window.operatorDashboardApp = new OperatorDashboardApp();

// Fonctions de compatibilité avec l'ancien code
window.logout = () => operatorDashboardApp.logout();
window.showSection = (sectionName) => operatorNavigation.navigateTo(sectionName);

// Initialisation automatique quand le DOM est prêt
document.addEventListener('DOMContentLoaded', () => {
    operatorDashboardApp.initialize();
});

// Nettoyage avant fermeture de la page
window.addEventListener('beforeunload', () => {
    operatorDashboardApp.cleanup();
});
