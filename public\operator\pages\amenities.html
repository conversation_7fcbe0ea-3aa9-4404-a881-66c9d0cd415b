<!-- Page Gestion des Commodités -->
<div id="amenitiesPage">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Commodités</h3>
        <button class="btn btn-primary" onclick="showCreateAmenityModal()">
            <i class="fas fa-plus me-2"></i>Nouvelle commodité
        </button>
    </div>

    <!-- Grille des commodités -->
    <div id="amenitiesGridContainer">
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
            <p class="mt-2 text-muted">Chargement des commodités...</p>
        </div>
    </div>
</div>

<script>
/**
 * Variables globales pour la gestion des commodités
 */
let allAmenities = [];

/**
 * Initialisation de la page des commodités
 */
async function initAmenitiesPage() {
    try {
        await loadAmenities();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page des commodités:', error);
        operatorUIManager.showAlert('Erreur lors du chargement des commodités', 'error');
    }
}

/**
 * Charge la liste des commodités
 */
async function loadAmenities() {
    const container = document.getElementById('amenitiesGridContainer');
    const loadingId = operatorUIManager.showLoading(container, 'Chargement des commodités...');

    try {
        const data = await operatorDataCache.getAmenities();
        allAmenities = data.amenities || [];
        
        displayAmenities();
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        operatorUIManager.hideLoading(loadingId);
        throw error;
    }
}

/**
 * Affiche la grille des commodités
 */
function displayAmenities() {
    const container = document.getElementById('amenitiesGridContainer');
    
    if (allAmenities.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-star fa-3x text-muted mb-3"></i>
                <h5>Aucune commodité</h5>
                <p class="text-muted">Commencez par ajouter des commodités pour vos bus</p>
                <button class="btn btn-primary" onclick="showCreateAmenityModal()">
                    <i class="fas fa-plus me-2"></i>Ajouter une commodité
                </button>
            </div>
        `;
        return;
    }

    let gridHtml = '<div class="row">';
    
    allAmenities.forEach(amenity => {
        gridHtml += `
            <div class="col-md-4 col-lg-3 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-2x text-primary mb-3"></i>
                        <h6 class="card-title">${amenity.name}</h6>
                        <p class="card-text text-muted small">${amenity.description || 'Aucune description'}</p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <button class="btn btn-sm btn-outline-primary" onclick="editAmenity(${amenity.id})" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAmenity(${amenity.id})" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    gridHtml += '</div>';
    container.innerHTML = gridHtml;
}

/**
 * Affiche la modale de création d'une commodité
 */
function showCreateAmenityModal() {
    operatorUIManager.resetForm('amenityForm');
    operatorUIManager.showModal('amenityModal', { title: 'Nouvelle Commodité' });
}

/**
 * Modifie une commodité
 * @param {number} amenityId - ID de la commodité
 */
async function editAmenity(amenityId) {
    try {
        const amenity = allAmenities.find(a => a.id === amenityId);
        if (!amenity) {
            operatorUIManager.showAlert('Commodité non trouvée', 'error');
            return;
        }

        await operatorFormHandler.prepareFormForEdit('amenityForm', amenity);
        operatorUIManager.showModal('amenityModal', { title: 'Modifier la Commodité' });
    } catch (error) {
        console.error('Erreur lors de la modification de la commodité:', error);
        operatorUIManager.showAlert('Erreur lors du chargement de la commodité', 'error');
    }
}

/**
 * Supprime une commodité
 * @param {number} amenityId - ID de la commodité
 */
async function deleteAmenity(amenityId) {
    const amenity = allAmenities.find(a => a.id === amenityId);
    if (!amenity) return;

    const confirmed = confirm(`Êtes-vous sûr de vouloir supprimer la commodité "${amenity.name}" ?`);
    if (!confirmed) return;

    try {
        await operatorApiClient.deleteAmenity(amenityId);
        operatorUIManager.showAlert('Commodité supprimée avec succès', 'success');
        
        // Invalide le cache et recharge
        operatorDataCache.invalidateAfterUpdate('amenities');
        await loadAmenities();
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        operatorUIManager.showAlert('Erreur lors de la suppression', 'error');
    }
}

/**
 * Sauvegarde une commodité (appelée par le gestionnaire de formulaires)
 */
async function saveAmenity() {
    const formData = operatorUIManager.getFormData('amenityForm');
    const isEdit = !!formData.amenityId;

    try {
        if (isEdit) {
            await operatorApiClient.updateAmenity(formData.amenityId, formData);
            operatorUIManager.showAlert('Commodité mise à jour avec succès', 'success');
        } else {
            await operatorApiClient.createAmenity(formData);
            operatorUIManager.showAlert('Commodité créée avec succès', 'success');
        }

        operatorUIManager.hideModal('amenityModal');
        operatorDataCache.invalidateAfterUpdate('amenities');
        await loadAmenities();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        operatorUIManager.showAlert(`Erreur: ${error.message}`, 'error');
    }
}
</script>
