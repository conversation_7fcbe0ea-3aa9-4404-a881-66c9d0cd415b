<!-- Page Gestion des Bus -->
<div id="busesPage">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Bus</h3>
        <button class="btn btn-primary" onclick="showCreateBusModal()">
            <i class="fas fa-plus me-2"></i>Nouveau bus
        </button>
    </div>

    <!-- Filtres pour les bus -->
    <div class="chart-container mb-4">
        <div class="row">
            <div class="col-md-3">
                <label for="busStatusFilter" class="form-label">Statut</label>
                <select class="form-select" id="busStatusFilter" onchange="filterBuses()">
                    <option value="">Tous les statuts</option>
                    <option value="active">Actif</option>
                    <option value="under_maintenance">En maintenance</option>
                    <option value="inactive">Inactif</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="busTypeFilter" class="form-label">Type</label>
                <select class="form-select" id="busTypeFilter" onchange="filterBuses()">
                    <option value="">Tous les types</option>
                    <option value="standard">Standard</option>
                    <option value="vip">VIP</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="busSearchFilter" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="busSearchFilter"
                       placeholder="Numéro d'immatriculation, marque..." onkeyup="filterBuses()">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearBusFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>

    <!-- Grille des bus -->
    <div class="chart-container">
        <div id="busesGridContainer">
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des bus...</p>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Variables globales pour la gestion des bus
 */
let allBuses = [];
let filteredBuses = [];

/**
 * Initialisation de la page des bus
 */
async function initBusesPage() {
    try {
        await loadBuses();
        setupBusFilters();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page des bus:', error);
        operatorUIManager.showAlert('Erreur lors du chargement des bus', 'error');
    }
}

/**
 * Charge la liste des bus
 */
async function loadBuses() {
    const container = document.getElementById('busesGridContainer');
    const loadingId = operatorUIManager.showLoading(container, 'Chargement des bus...');

    try {
        const data = await operatorDataCache.getBuses();
        allBuses = data.buses || [];
        filteredBuses = [...allBuses];
        
        displayBuses();
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        operatorUIManager.hideLoading(loadingId);
        throw error;
    }
}

/**
 * Affiche la grille des bus
 */
function displayBuses() {
    const container = document.getElementById('busesGridContainer');
    
    if (filteredBuses.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-bus fa-3x text-muted mb-3"></i>
                <h5>Aucun bus trouvé</h5>
                <p class="text-muted">Ajoutez des bus à votre flotte</p>
                <button class="btn btn-primary" onclick="showCreateBusModal()">
                    <i class="fas fa-plus me-2"></i>Ajouter un bus
                </button>
            </div>
        `;
        return;
    }

    let gridHtml = '<div class="row">';
    
    filteredBuses.forEach(bus => {
        const statusConfig = getBusStatusConfig(bus.status);
        
        gridHtml += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h6 class="card-title mb-0">${bus.registration_number}</h6>
                            <span class="badge ${statusConfig.class}">${statusConfig.text}</span>
                        </div>
                        
                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <small class="text-muted">Capacité</small>
                                <div class="fw-bold">${bus.capacity} places</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Type</small>
                                <div class="fw-bold">${bus.type === 'vip' ? 'VIP' : 'Standard'}</div>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">Marque/Modèle</small>
                            <div>${bus.brand} ${bus.model}</div>
                        </div>
                        
                        ${bus.year_manufactured ? `
                            <div class="mb-2">
                                <small class="text-muted">Année</small>
                                <div>${bus.year_manufactured}</div>
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100 mb-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="editBus(${bus.id})" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="manageBusSeats(${bus.id})" title="Gérer sièges">
                                <i class="fas fa-chair"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="viewBusTrips(${bus.id})" title="Voir voyages">
                                <i class="fas fa-route"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteBus(${bus.id})" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <button class="btn btn-sm btn-outline-warning w-100" onclick="toggleBusStatus(${bus.id})">
                            ${bus.status === 'active' ? 'Mettre en maintenance' : 'Activer'}
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    gridHtml += '</div>';
    container.innerHTML = gridHtml;
}

/**
 * Retourne la configuration d'affichage pour un statut de bus
 * @param {string} status - Statut du bus
 * @returns {Object} - Configuration d'affichage
 */
function getBusStatusConfig(status) {
    const configs = {
        'active': { class: 'bg-success', text: 'Actif' },
        'under_maintenance': { class: 'bg-warning', text: 'En maintenance' },
        'inactive': { class: 'bg-secondary', text: 'Inactif' }
    };
    return configs[status] || { class: 'bg-secondary', text: status };
}

/**
 * Configure les filtres
 */
function setupBusFilters() {
    // Écouteur pour la recherche en temps réel
    const searchInput = document.getElementById('busSearchFilter');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(filterBuses, 300);
        });
    }
}

/**
 * Filtre les bus selon les critères
 */
function filterBuses() {
    const statusFilter = document.getElementById('busStatusFilter')?.value || '';
    const typeFilter = document.getElementById('busTypeFilter')?.value || '';
    const searchFilter = document.getElementById('busSearchFilter')?.value.toLowerCase() || '';

    filteredBuses = allBuses.filter(bus => {
        const matchesStatus = !statusFilter || bus.status === statusFilter;
        const matchesType = !typeFilter || bus.type === typeFilter;
        const matchesSearch = !searchFilter || 
            bus.registration_number.toLowerCase().includes(searchFilter) ||
            bus.brand.toLowerCase().includes(searchFilter) ||
            bus.model.toLowerCase().includes(searchFilter);

        return matchesStatus && matchesType && matchesSearch;
    });

    displayBuses();
}

/**
 * Efface tous les filtres
 */
function clearBusFilters() {
    document.getElementById('busStatusFilter').value = '';
    document.getElementById('busTypeFilter').value = '';
    document.getElementById('busSearchFilter').value = '';
    
    filteredBuses = [...allBuses];
    displayBuses();
}

/**
 * Affiche la modale de création d'un bus
 */
async function showCreateBusModal() {
    try {
        operatorUIManager.resetForm('busForm');
        await operatorFormHandler.loadFormDependencies('busForm');
        operatorUIManager.showModal('busModal', { title: 'Nouveau Bus' });
    } catch (error) {
        console.error('Erreur lors de l\'ouverture de la modale:', error);
        operatorUIManager.showAlert('Erreur lors de l\'ouverture du formulaire', 'error');
    }
}

/**
 * Modifie un bus
 * @param {number} busId - ID du bus
 */
async function editBus(busId) {
    try {
        const bus = allBuses.find(b => b.id === busId);
        if (!bus) {
            operatorUIManager.showAlert('Bus non trouvé', 'error');
            return;
        }

        await operatorFormHandler.prepareFormForEdit('busForm', bus);
        operatorUIManager.showModal('busModal', { title: 'Modifier le Bus' });
    } catch (error) {
        console.error('Erreur lors de la modification du bus:', error);
        operatorUIManager.showAlert('Erreur lors du chargement du bus', 'error');
    }
}

/**
 * Gère les sièges d'un bus
 * @param {number} busId - ID du bus
 */
function manageBusSeats(busId) {
    // Navigue vers la page des sièges avec le bus sélectionné
    operatorNavigation.navigateTo('seats').then(() => {
        if (typeof window.filterSeatsByBus === 'function') {
            window.filterSeatsByBus(busId);
        }
    });
}

/**
 * Affiche les voyages d'un bus
 * @param {number} busId - ID du bus
 */
function viewBusTrips(busId) {
    // Navigue vers la page des voyages avec le bus sélectionné
    operatorNavigation.navigateTo('trips').then(() => {
        if (typeof window.filterTripsByBus === 'function') {
            window.filterTripsByBus(busId);
        }
    });
}

/**
 * Change le statut d'un bus
 * @param {number} busId - ID du bus
 */
async function toggleBusStatus(busId) {
    const bus = allBuses.find(b => b.id === busId);
    if (!bus) return;

    const newStatus = bus.status === 'active' ? 'under_maintenance' : 'active';
    const action = newStatus === 'active' ? 'activer' : 'mettre en maintenance';
    
    const confirmed = confirm(`Êtes-vous sûr de vouloir ${action} le bus "${bus.registration_number}" ?`);
    if (!confirmed) return;

    try {
        await operatorApiClient.put(`operator/buses/${busId}`, { status: newStatus });
        operatorUIManager.showAlert(`Bus ${action === 'activer' ? 'activé' : 'mis en maintenance'} avec succès`, 'success');
        
        // Invalide le cache et recharge
        operatorDataCache.invalidateAfterUpdate('buses');
        await loadBuses();
    } catch (error) {
        console.error('Erreur lors du changement de statut:', error);
        operatorUIManager.showAlert('Erreur lors du changement de statut', 'error');
    }
}

/**
 * Supprime un bus
 * @param {number} busId - ID du bus
 */
async function deleteBus(busId) {
    const bus = allBuses.find(b => b.id === busId);
    if (!bus) return;

    const confirmed = confirm(`Êtes-vous sûr de vouloir supprimer le bus "${bus.registration_number}" ?`);
    if (!confirmed) return;

    try {
        await operatorApiClient.delete(`operator/buses/${busId}`);
        operatorUIManager.showAlert('Bus supprimé avec succès', 'success');
        
        // Invalide le cache et recharge
        operatorDataCache.invalidateAfterUpdate('buses');
        await loadBuses();
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        operatorUIManager.showAlert('Erreur lors de la suppression', 'error');
    }
}

/**
 * Sauvegarde un bus (appelée par le gestionnaire de formulaires)
 */
async function saveBus() {
    const formData = operatorUIManager.getFormData('busForm');
    const isEdit = !!formData.busId;

    try {
        if (isEdit) {
            await operatorApiClient.put(`operator/buses/${formData.busId}`, formData);
            operatorUIManager.showAlert('Bus mis à jour avec succès', 'success');
        } else {
            await operatorApiClient.post('operator/buses', formData);
            operatorUIManager.showAlert('Bus créé avec succès', 'success');
        }

        operatorUIManager.hideModal('busModal');
        operatorDataCache.invalidateAfterUpdate('buses');
        await loadBuses();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        operatorUIManager.showAlert(`Erreur: ${error.message}`, 'error');
    }
}
</script>
