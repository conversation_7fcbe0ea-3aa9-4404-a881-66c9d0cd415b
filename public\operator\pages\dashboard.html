<!-- Page Tableau de bord -->
<div id="dashboardPage">
    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-primary text-white">
                    <i class="fas fa-ticket-alt"></i>
                </div>
                <h3 class="mb-1" id="totalBookingsToday">0</h3>
                <p class="text-muted mb-0">Réservations aujourd'hui</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-success text-white">
                    <i class="fas fa-bus"></i>
                </div>
                <h3 class="mb-1" id="activeTrips">0</h3>
                <p class="text-muted mb-0">Voyages en cours</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-warning text-white">
                    <i class="fas fa-coins"></i>
                </div>
                <h3 class="mb-1" id="revenueToday">0 FCFA</h3>
                <p class="text-muted mb-0">Revenus aujourd'hui</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card">
                <div class="stats-icon bg-info text-white">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="mb-1" id="totalPassengers">0</h3>
                <p class="text-muted mb-0">Passagers aujourd'hui</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Graphique des revenus -->
        <div class="col-lg-8 mb-4">
            <div class="chart-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Évolution des revenus (7 derniers jours)</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary active" onclick="loadRevenueChart('7days')">7j</button>
                        <button class="btn btn-outline-primary" onclick="loadRevenueChart('30days')">30j</button>
                        <button class="btn btn-outline-primary" onclick="loadRevenueChart('90days')">90j</button>
                    </div>
                </div>
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>

        <!-- Alertes et notifications -->
        <div class="col-lg-4 mb-4">
            <div class="chart-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Alertes</h5>
                    <button class="btn btn-sm btn-outline-secondary" onclick="refreshAlerts()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div id="alertsContainer">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="mt-2 text-muted">Chargement...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Voyages du jour -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>Voyages d'aujourd'hui</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="operatorNavigation.navigateTo('trips')">
                            <i class="fas fa-plus me-1"></i>Nouveau voyage
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="refreshTodayTrips()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div id="todayTripsContainer">
                    <div class="text-center py-4">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="mt-2 text-muted">Chargement...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Actions rapides</h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100 h-100 py-3" onclick="operatorNavigation.navigateTo('validation')">
                            <i class="fas fa-qrcode fa-2x d-block mb-2"></i>
                            Valider un ticket
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-success w-100 h-100 py-3" onclick="operatorNavigation.navigateTo('bookings')">
                            <i class="fas fa-ticket-alt fa-2x d-block mb-2"></i>
                            Voir réservations
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-info w-100 h-100 py-3" onclick="operatorNavigation.navigateTo('trips')">
                            <i class="fas fa-route fa-2x d-block mb-2"></i>
                            Gérer voyages
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-warning w-100 h-100 py-3" onclick="operatorNavigation.navigateTo('reports')">
                            <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                            Voir rapports
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Initialisation de la page tableau de bord
 */
async function initDashboardPage() {
    try {
        await operatorDashboardStats.initialize();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation du tableau de bord:', error);
        operatorUIManager.showAlert('Erreur lors du chargement du tableau de bord', 'error');
    }
}

/**
 * Charge le graphique des revenus pour une période donnée
 * @param {string} period - Période (7days, 30days, 90days)
 */
async function loadRevenueChart(period) {
    try {
        // Met à jour les boutons actifs
        document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');

        // Charge les nouvelles données
        const data = await operatorApiClient.get(`operator/analytics/revenue?period=${period}`);
        operatorDashboardStats.updateRevenueChart(data.chart_data || []);
    } catch (error) {
        console.error('Erreur lors du chargement du graphique:', error);
        operatorUIManager.showAlert('Erreur lors du chargement du graphique', 'error');
    }
}

/**
 * Rafraîchit les alertes
 */
async function refreshAlerts() {
    try {
        const data = await operatorApiClient.get('operator/alerts');
        operatorDashboardStats.updateAlertsSection(data.alerts || []);
    } catch (error) {
        console.error('Erreur lors du rafraîchissement des alertes:', error);
        operatorUIManager.showAlert('Erreur lors du rafraîchissement', 'error');
    }
}

/**
 * Rafraîchit les voyages du jour
 */
async function refreshTodayTrips() {
    try {
        const data = await operatorApiClient.get('operator/trips/today');
        operatorDashboardStats.updateTodayTrips(data.trips || []);
    } catch (error) {
        console.error('Erreur lors du rafraîchissement des voyages:', error);
        operatorUIManager.showAlert('Erreur lors du rafraîchissement', 'error');
    }
}

/**
 * Affiche les détails d'un voyage
 * @param {number} tripId - ID du voyage
 */
function viewTripDetails(tripId) {
    // Navigue vers la page des voyages avec le voyage sélectionné
    operatorNavigation.navigateTo('trips').then(() => {
        // Code pour sélectionner/afficher le voyage spécifique
        if (typeof window.selectTrip === 'function') {
            window.selectTrip(tripId);
        }
    });
}

/**
 * Génère un rapport rapide
 * @param {string} type - Type de rapport
 */
async function generateQuickReport(type) {
    try {
        switch (type) {
            case 'revenue':
                await operatorDashboardStats.generateRevenueReport();
                break;
            case 'occupancy':
                await operatorDashboardStats.generateOccupancyReport();
                break;
            default:
                operatorNavigation.navigateTo('reports');
        }
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}
</script>
