<!-- Page Gestion des Lieux/Villes -->
<div id="locationsPage">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Lieux/Villes</h3>
        <button class="btn btn-primary" onclick="showCreateLocationModal()">
            <i class="fas fa-plus me-2"></i>Nouveau lieu
        </button>
    </div>

    <!-- Filtres pour les lieux -->
    <div class="chart-container mb-4">
        <div class="row">
            <div class="col-md-3">
                <label for="locationStatusFilter" class="form-label">Statut</label>
                <select class="form-select" id="locationStatusFilter" onchange="filterLocations()">
                    <option value="">Tous les statuts</option>
                    <option value="active">Actif</option>
                    <option value="inactive">Inactif</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="locationCountryFilter" class="form-label">Pays</label>
                <select class="form-select" id="locationCountryFilter" onchange="filterLocations()">
                    <option value="">Tous les pays</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="locationSearchFilter" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="locationSearchFilter"
                       placeholder="Nom de lieu, région..." onkeyup="filterLocations()">
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearLocationFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>

    <!-- Tableau des lieux -->
    <div class="chart-container">
        <div id="locationsTableContainer">
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des lieux...</p>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Variables globales pour la gestion des lieux
 */
let allLocations = [];
let filteredLocations = [];

/**
 * Initialisation de la page des lieux
 */
async function initLocationsPage() {
    try {
        await loadLocations();
        setupLocationFilters();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page des lieux:', error);
        operatorUIManager.showAlert('Erreur lors du chargement des lieux', 'error');
    }
}

/**
 * Charge la liste des lieux
 */
async function loadLocations() {
    const container = document.getElementById('locationsTableContainer');
    const loadingId = operatorUIManager.showLoading(container, 'Chargement des lieux...');

    try {
        const data = await operatorDataCache.getLocations();
        allLocations = data.locations || [];
        filteredLocations = [...allLocations];
        
        displayLocations();
        populateCountryFilter();
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        operatorUIManager.hideLoading(loadingId);
        throw error;
    }
}

/**
 * Affiche la liste des lieux
 */
function displayLocations() {
    const container = document.getElementById('locationsTableContainer');
    
    const columns = [
        { field: 'name', title: 'Nom' },
        { field: 'region', title: 'Région' },
        { field: 'country', title: 'Pays' },
        { field: 'timezone', title: 'Fuseau horaire' },
        { 
            field: 'status', 
            title: 'Statut',
            render: (value) => {
                const statusClass = value === 'active' ? 'bg-success' : 'bg-secondary';
                const statusText = value === 'active' ? 'Actif' : 'Inactif';
                return `<span class="badge ${statusClass}">${statusText}</span>`;
            }
        },
        {
            field: 'created_at',
            title: 'Créé le',
            render: (value) => new Date(value).toLocaleDateString('fr-FR')
        }
    ];

    const actions = [
        {
            icon: 'fas fa-edit',
            title: 'Modifier',
            variant: 'primary',
            onclick: 'editLocation({id})'
        },
        {
            icon: 'fas fa-map-pin',
            title: 'Voir arrêts',
            variant: 'info',
            onclick: 'viewLocationStops({id})'
        },
        {
            icon: 'fas fa-trash',
            title: 'Supprimer',
            variant: 'danger',
            onclick: 'deleteLocation({id})'
        }
    ];

    const tableHtml = operatorUIManager.createTable(filteredLocations, columns, { actions });
    container.innerHTML = tableHtml;
}

/**
 * Configure les filtres
 */
function setupLocationFilters() {
    // Écouteur pour la recherche en temps réel
    const searchInput = document.getElementById('locationSearchFilter');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(filterLocations, 300);
        });
    }
}

/**
 * Remplit le filtre des pays
 */
function populateCountryFilter() {
    const countries = [...new Set(allLocations.map(location => location.country))].sort();
    const countryFilter = document.getElementById('locationCountryFilter');
    
    if (countryFilter) {
        // Garde l'option par défaut
        const defaultOption = countryFilter.querySelector('option[value=""]');
        countryFilter.innerHTML = '';
        countryFilter.appendChild(defaultOption);
        
        countries.forEach(country => {
            const option = document.createElement('option');
            option.value = country;
            option.textContent = country;
            countryFilter.appendChild(option);
        });
    }
}

/**
 * Filtre les lieux selon les critères
 */
function filterLocations() {
    const statusFilter = document.getElementById('locationStatusFilter')?.value || '';
    const countryFilter = document.getElementById('locationCountryFilter')?.value || '';
    const searchFilter = document.getElementById('locationSearchFilter')?.value.toLowerCase() || '';

    filteredLocations = allLocations.filter(location => {
        const matchesStatus = !statusFilter || location.status === statusFilter;
        const matchesCountry = !countryFilter || location.country === countryFilter;
        const matchesSearch = !searchFilter || 
            location.name.toLowerCase().includes(searchFilter) ||
            location.region.toLowerCase().includes(searchFilter) ||
            location.country.toLowerCase().includes(searchFilter);

        return matchesStatus && matchesCountry && matchesSearch;
    });

    displayLocations();
}

/**
 * Efface tous les filtres
 */
function clearLocationFilters() {
    document.getElementById('locationStatusFilter').value = '';
    document.getElementById('locationCountryFilter').value = '';
    document.getElementById('locationSearchFilter').value = '';
    
    filteredLocations = [...allLocations];
    displayLocations();
}

/**
 * Affiche la modale de création d'un lieu
 */
function showCreateLocationModal() {
    operatorUIManager.resetForm('locationForm');
    operatorUIManager.showModal('locationModal', { title: 'Nouveau Lieu' });
}

/**
 * Modifie un lieu
 * @param {number} locationId - ID du lieu
 */
async function editLocation(locationId) {
    try {
        const location = allLocations.find(l => l.id === locationId);
        if (!location) {
            operatorUIManager.showAlert('Lieu non trouvé', 'error');
            return;
        }

        await operatorFormHandler.prepareFormForEdit('locationForm', location);
        operatorUIManager.showModal('locationModal', { title: 'Modifier le Lieu' });
    } catch (error) {
        console.error('Erreur lors de la modification du lieu:', error);
        operatorUIManager.showAlert('Erreur lors du chargement du lieu', 'error');
    }
}

/**
 * Supprime un lieu
 * @param {number} locationId - ID du lieu
 */
async function deleteLocation(locationId) {
    const location = allLocations.find(l => l.id === locationId);
    if (!location) return;

    const confirmed = confirm(`Êtes-vous sûr de vouloir supprimer le lieu "${location.name}" ?`);
    if (!confirmed) return;

    try {
        await operatorApiClient.deleteLocation(locationId);
        operatorUIManager.showAlert('Lieu supprimé avec succès', 'success');
        
        // Invalide le cache et recharge
        operatorDataCache.invalidateAfterUpdate('locations');
        await loadLocations();
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        operatorUIManager.showAlert('Erreur lors de la suppression', 'error');
    }
}

/**
 * Affiche les arrêts d'un lieu
 * @param {number} locationId - ID du lieu
 */
function viewLocationStops(locationId) {
    // Navigue vers la page des arrêts avec le lieu sélectionné
    operatorNavigation.navigateTo('stops').then(() => {
        // Filtre par lieu si la fonction existe
        if (typeof window.filterStopsByLocation === 'function') {
            window.filterStopsByLocation(locationId);
        }
    });
}

/**
 * Sauvegarde un lieu (appelée par le gestionnaire de formulaires)
 */
async function saveLocation() {
    const formData = operatorUIManager.getFormData('locationForm');
    const isEdit = !!formData.locationId;

    try {
        if (isEdit) {
            await operatorApiClient.updateLocation(formData.locationId, formData);
            operatorUIManager.showAlert('Lieu mis à jour avec succès', 'success');
        } else {
            await operatorApiClient.createLocation(formData);
            operatorUIManager.showAlert('Lieu créé avec succès', 'success');
        }

        operatorUIManager.hideModal('locationModal');
        operatorDataCache.invalidateAfterUpdate('locations');
        await loadLocations();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        operatorUIManager.showAlert(`Erreur: ${error.message}`, 'error');
    }
}

/**
 * Exporte les lieux au format CSV
 */
function exportLocations() {
    const csvContent = [
        ['Nom', 'Région', 'Pays', 'Fuseau horaire', 'Latitude', 'Longitude', 'Statut'],
        ...filteredLocations.map(location => [
            location.name,
            location.region,
            location.country,
            location.timezone,
            location.latitude,
            location.longitude,
            location.status
        ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `lieux_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}
</script>
