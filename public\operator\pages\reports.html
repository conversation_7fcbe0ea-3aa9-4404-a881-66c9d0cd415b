<!-- Page Rapports et analyses -->
<div id="reportsPage">
    <h3 class="mb-4">Rapports et analyses</h3>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="chart-container text-center">
                <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                <h5>Rapport de revenus</h5>
                <p class="text-muted">Analyse des revenus par période</p>
                <button class="btn btn-outline-primary" onclick="generateRevenueReport()">
                    <i class="fas fa-chart-line me-2"></i>Générer
                </button>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="chart-container text-center">
                <i class="fas fa-chart-pie fa-3x text-success mb-3"></i>
                <h5>Rapport d'occupation</h5>
                <p class="text-muted">Taux d'occupation des bus</p>
                <button class="btn btn-outline-primary" onclick="generateOccupancyReport()">
                    <i class="fas fa-chart-pie me-2"></i>Générer
                </button>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="chart-container text-center">
                <i class="fas fa-route fa-3x text-info mb-3"></i>
                <h5>Rapport des trajets</h5>
                <p class="text-muted">Performance par trajet</p>
                <button class="btn btn-outline-primary" onclick="generateRoutesReport()">
                    <i class="fas fa-route me-2"></i>Générer
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="chart-container text-center">
                <i class="fas fa-users fa-3x text-warning mb-3"></i>
                <h5>Rapport des passagers</h5>
                <p class="text-muted">Statistiques des passagers</p>
                <button class="btn btn-outline-primary" onclick="generatePassengersReport()">
                    <i class="fas fa-users me-2"></i>Générer
                </button>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="chart-container text-center">
                <i class="fas fa-credit-card fa-3x text-danger mb-3"></i>
                <h5>Rapport des paiements</h5>
                <p class="text-muted">Analyse des paiements</p>
                <button class="btn btn-outline-primary" onclick="generatePaymentsReport()">
                    <i class="fas fa-credit-card me-2"></i>Générer
                </button>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="chart-container text-center">
                <i class="fas fa-bus fa-3x text-secondary mb-3"></i>
                <h5>Rapport de flotte</h5>
                <p class="text-muted">Utilisation de la flotte</p>
                <button class="btn btn-outline-primary" onclick="generateFleetReport()">
                    <i class="fas fa-bus me-2"></i>Générer
                </button>
            </div>
        </div>
    </div>

    <div id="reportResultContainer" class="chart-container" style="display: none;">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 id="reportTitle">Résultats du rapport</h5>
            <div>
                <button class="btn btn-sm btn-outline-primary me-2" onclick="exportCurrentReport()">
                    <i class="fas fa-download me-1"></i>Exporter
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="closeReport()">
                    <i class="fas fa-times me-1"></i>Fermer
                </button>
            </div>
        </div>
        <div id="reportContent"></div>
    </div>
</div>

<script>
/**
 * Variables globales pour les rapports
 */
let currentReportData = null;
let currentReportType = null;

/**
 * Initialisation de la page des rapports
 */
async function initReportsPage() {
    try {
        // Aucune initialisation spéciale nécessaire pour l'instant
        console.log('Page des rapports initialisée');
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page des rapports:', error);
        operatorUIManager.showAlert('Erreur lors de l\'initialisation', 'error');
    }
}

/**
 * Génère un rapport de revenus
 */
async function generateRevenueReport() {
    try {
        const period = await showPeriodSelector();
        if (!period) return;

        const loadingId = operatorUIManager.showLoading('#reportResultContainer', 'Génération du rapport...');
        
        const data = await operatorApiClient.get(`operator/reports/revenue?period=${period}`);
        
        currentReportData = data;
        currentReportType = 'revenue';
        
        const reportHtml = formatRevenueReport(data);
        showReport('Rapport de revenus', reportHtml);
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}

/**
 * Génère un rapport d'occupation
 */
async function generateOccupancyReport() {
    try {
        const period = await showPeriodSelector();
        if (!period) return;

        const loadingId = operatorUIManager.showLoading('#reportResultContainer', 'Génération du rapport...');
        
        const data = await operatorApiClient.get(`operator/reports/occupancy?period=${period}`);
        
        currentReportData = data;
        currentReportType = 'occupancy';
        
        const reportHtml = formatOccupancyReport(data);
        showReport('Rapport d\'occupation', reportHtml);
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}

/**
 * Génère un rapport des trajets
 */
async function generateRoutesReport() {
    try {
        const period = await showPeriodSelector();
        if (!period) return;

        const loadingId = operatorUIManager.showLoading('#reportResultContainer', 'Génération du rapport...');
        
        const data = await operatorApiClient.get(`operator/reports/routes?period=${period}`);
        
        currentReportData = data;
        currentReportType = 'routes';
        
        const reportHtml = formatRoutesReport(data);
        showReport('Rapport des trajets', reportHtml);
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}

/**
 * Génère un rapport des passagers
 */
async function generatePassengersReport() {
    try {
        const period = await showPeriodSelector();
        if (!period) return;

        const loadingId = operatorUIManager.showLoading('#reportResultContainer', 'Génération du rapport...');
        
        const data = await operatorApiClient.get(`operator/reports/passengers?period=${period}`);
        
        currentReportData = data;
        currentReportType = 'passengers';
        
        const reportHtml = formatPassengersReport(data);
        showReport('Rapport des passagers', reportHtml);
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}

/**
 * Génère un rapport des paiements
 */
async function generatePaymentsReport() {
    try {
        const period = await showPeriodSelector();
        if (!period) return;

        const loadingId = operatorUIManager.showLoading('#reportResultContainer', 'Génération du rapport...');
        
        const data = await operatorApiClient.get(`operator/reports/payments?period=${period}`);
        
        currentReportData = data;
        currentReportType = 'payments';
        
        const reportHtml = formatPaymentsReport(data);
        showReport('Rapport des paiements', reportHtml);
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}

/**
 * Génère un rapport de flotte
 */
async function generateFleetReport() {
    try {
        const period = await showPeriodSelector();
        if (!period) return;

        const loadingId = operatorUIManager.showLoading('#reportResultContainer', 'Génération du rapport...');
        
        const data = await operatorApiClient.get(`operator/reports/fleet?period=${period}`);
        
        currentReportData = data;
        currentReportType = 'fleet';
        
        const reportHtml = formatFleetReport(data);
        showReport('Rapport de flotte', reportHtml);
        
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        console.error('Erreur lors de la génération du rapport:', error);
        operatorUIManager.showAlert('Erreur lors de la génération du rapport', 'error');
    }
}

/**
 * Affiche un sélecteur de période
 * @returns {Promise<string>} - Période sélectionnée
 */
function showPeriodSelector() {
    return new Promise((resolve) => {
        const modalHtml = `
            <div class="modal fade" id="periodSelectorModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Sélectionner la période</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Période</label>
                                <select class="form-select" id="periodSelect">
                                    <option value="7days">7 derniers jours</option>
                                    <option value="30days">30 derniers jours</option>
                                    <option value="90days">90 derniers jours</option>
                                    <option value="current_month">Mois en cours</option>
                                    <option value="last_month">Mois dernier</option>
                                    <option value="current_year">Année en cours</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="resolvePeriod(null)">Annuler</button>
                            <button type="button" class="btn btn-primary" onclick="resolvePeriod(document.getElementById('periodSelect').value)">Générer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprime la modale existante
        const existingModal = document.getElementById('periodSelectorModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        const modal = new bootstrap.Modal(document.getElementById('periodSelectorModal'));
        modal.show();

        // Fonction globale pour résoudre la promesse
        window.resolvePeriod = (period) => {
            modal.hide();
            resolve(period);
            delete window.resolvePeriod;
        };
    });
}

/**
 * Affiche un rapport
 * @param {string} title - Titre du rapport
 * @param {string} content - Contenu HTML du rapport
 */
function showReport(title, content) {
    document.getElementById('reportTitle').textContent = title;
    document.getElementById('reportContent').innerHTML = content;
    document.getElementById('reportResultContainer').style.display = 'block';
    
    // Scroll vers le rapport
    document.getElementById('reportResultContainer').scrollIntoView({ behavior: 'smooth' });
}

/**
 * Ferme le rapport affiché
 */
function closeReport() {
    document.getElementById('reportResultContainer').style.display = 'none';
    currentReportData = null;
    currentReportType = null;
}

/**
 * Formate un rapport de revenus
 * @param {Object} data - Données du rapport
 * @returns {string} - HTML formaté
 */
function formatRevenueReport(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>Résumé</h6>
                <table class="table table-sm">
                    <tr><td>Revenus totaux</td><td class="fw-bold">${formatCurrency(data.total_revenue || 0)}</td></tr>
                    <tr><td>Nombre de réservations</td><td class="fw-bold">${data.total_bookings || 0}</td></tr>
                    <tr><td>Revenu moyen par réservation</td><td class="fw-bold">${formatCurrency(data.average_revenue || 0)}</td></tr>
                    <tr><td>Croissance</td><td class="fw-bold ${data.growth >= 0 ? 'text-success' : 'text-danger'}">${data.growth || 0}%</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Top 5 des itinéraires</h6>
                <table class="table table-sm">
                    ${(data.top_routes || []).map(route => `
                        <tr>
                            <td>${route.name}</td>
                            <td class="fw-bold">${formatCurrency(route.revenue)}</td>
                        </tr>
                    `).join('')}
                </table>
            </div>
        </div>
        <div class="mt-3">
            <canvas id="revenueReportChart" height="100"></canvas>
        </div>
    `;
}

/**
 * Formate un rapport d'occupation
 * @param {Object} data - Données du rapport
 * @returns {string} - HTML formaté
 */
function formatOccupancyReport(data) {
    return `
        <div class="row">
            <div class="col-md-6">
                <h6>Taux d'occupation</h6>
                <table class="table table-sm">
                    <tr><td>Taux moyen</td><td class="fw-bold">${data.average_occupancy || 0}%</td></tr>
                    <tr><td>Meilleur taux</td><td class="fw-bold text-success">${data.best_occupancy || 0}%</td></tr>
                    <tr><td>Plus faible taux</td><td class="fw-bold text-danger">${data.worst_occupancy || 0}%</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Par type de bus</h6>
                <table class="table table-sm">
                    ${(data.by_bus_type || []).map(type => `
                        <tr>
                            <td>${type.type}</td>
                            <td class="fw-bold">${type.occupancy}%</td>
                        </tr>
                    `).join('')}
                </table>
            </div>
        </div>
    `;
}

/**
 * Formate la devise
 * @param {number} amount - Montant
 * @returns {string} - Montant formaté
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XOF',
        minimumFractionDigits: 0
    }).format(amount).replace('XOF', 'FCFA');
}

/**
 * Exporte le rapport actuel
 */
function exportCurrentReport() {
    if (!currentReportData || !currentReportType) {
        operatorUIManager.showAlert('Aucun rapport à exporter', 'warning');
        return;
    }

    // Génère un CSV basique
    const csvContent = generateCSVFromReport(currentReportData, currentReportType);
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rapport_${currentReportType}_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    operatorUIManager.showAlert('Rapport exporté avec succès', 'success');
}

/**
 * Génère un CSV à partir des données de rapport
 * @param {Object} data - Données du rapport
 * @param {string} type - Type de rapport
 * @returns {string} - Contenu CSV
 */
function generateCSVFromReport(data, type) {
    // Implémentation basique - peut être étendue selon les besoins
    const headers = ['Métrique', 'Valeur'];
    const rows = Object.entries(data).map(([key, value]) => [key, value]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
}

// Fonctions de formatage pour les autres types de rapports (à implémenter selon les besoins)
function formatRoutesReport(data) { return '<p>Rapport des trajets en cours de développement</p>'; }
function formatPassengersReport(data) { return '<p>Rapport des passagers en cours de développement</p>'; }
function formatPaymentsReport(data) { return '<p>Rapport des paiements en cours de développement</p>'; }
function formatFleetReport(data) { return '<p>Rapport de flotte en cours de développement</p>'; }
</script>
