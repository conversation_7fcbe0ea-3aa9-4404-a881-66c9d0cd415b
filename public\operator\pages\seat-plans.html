<!-- Page Gestion des Plans de Sièges -->
<div id="seatPlansPage">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Plans de Sièges</h3>
        <button class="btn btn-primary" onclick="showCreateSeatPlanModal()">
            <i class="fas fa-plus me-2"></i>Nouveau plan
        </button>
    </div>

    <!-- Grille des plans de sièges -->
    <div id="seatPlansGridContainer">
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
            <p class="mt-2 text-muted">Chargement des plans de sièges...</p>
        </div>
    </div>
</div>

<script>
/**
 * Variables globales pour la gestion des plans de sièges
 */
let allSeatPlans = [];

/**
 * Initialisation de la page des plans de sièges
 */
async function initSeatPlansPage() {
    try {
        await loadSeatPlans();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page des plans de sièges:', error);
        operatorUIManager.showAlert('Erreur lors du chargement des plans de sièges', 'error');
    }
}

/**
 * Charge la liste des plans de sièges
 */
async function loadSeatPlans() {
    const container = document.getElementById('seatPlansGridContainer');
    const loadingId = operatorUIManager.showLoading(container, 'Chargement des plans de sièges...');

    try {
        const data = await operatorDataCache.getSeatPlans();
        allSeatPlans = data.seat_plans || [];
        
        displaySeatPlans();
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        operatorUIManager.hideLoading(loadingId);
        throw error;
    }
}

/**
 * Affiche la grille des plans de sièges
 */
function displaySeatPlans() {
    const container = document.getElementById('seatPlansGridContainer');
    
    if (allSeatPlans.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-th fa-3x text-muted mb-3"></i>
                <h5>Aucun plan de sièges</h5>
                <p class="text-muted">Créez des plans de sièges pour organiser vos bus</p>
                <button class="btn btn-primary" onclick="showCreateSeatPlanModal()">
                    <i class="fas fa-plus me-2"></i>Créer un plan
                </button>
            </div>
        `;
        return;
    }

    let gridHtml = '<div class="row">';
    
    allSeatPlans.forEach(plan => {
        const totalSeats = plan.rows * plan.columns;
        
        gridHtml += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h6 class="card-title mb-0">${plan.configuration}</h6>
                            <span class="badge bg-primary">${totalSeats} sièges</span>
                        </div>
                        
                        <div class="seat-plan-preview mb-3">
                            ${generateSeatPlanPreview(plan)}
                        </div>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">Rangées</small>
                                <div class="fw-bold">${plan.rows}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Colonnes</small>
                                <div class="fw-bold">${plan.columns}</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <button class="btn btn-sm btn-outline-primary" onclick="editSeatPlan(${plan.id})" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="previewSeatPlan(${plan.id})" title="Aperçu">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteSeatPlan(${plan.id})" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    gridHtml += '</div>';
    container.innerHTML = gridHtml;
}

/**
 * Génère un aperçu miniature du plan de sièges
 * @param {Object} plan - Plan de sièges
 * @returns {string} - HTML de l'aperçu
 */
function generateSeatPlanPreview(plan) {
    let previewHtml = '<div class="seat-preview-grid">';
    
    for (let row = 0; row < Math.min(plan.rows, 4); row++) {
        previewHtml += '<div class="seat-preview-row">';
        for (let col = 0; col < Math.min(plan.columns, 6); col++) {
            previewHtml += '<div class="seat-preview-seat"></div>';
        }
        previewHtml += '</div>';
    }
    
    if (plan.rows > 4 || plan.columns > 6) {
        previewHtml += '<div class="text-center"><small class="text-muted">...</small></div>';
    }
    
    previewHtml += '</div>';
    
    return previewHtml;
}

/**
 * Affiche la modale de création d'un plan de sièges
 */
function showCreateSeatPlanModal() {
    operatorUIManager.resetForm('seatPlanForm');
    operatorUIManager.showModal('seatPlanModal', { title: 'Nouveau Plan de Sièges' });
}

/**
 * Modifie un plan de sièges
 * @param {number} planId - ID du plan
 */
async function editSeatPlan(planId) {
    try {
        const plan = allSeatPlans.find(p => p.id === planId);
        if (!plan) {
            operatorUIManager.showAlert('Plan de sièges non trouvé', 'error');
            return;
        }

        await operatorFormHandler.prepareFormForEdit('seatPlanForm', plan);
        operatorUIManager.showModal('seatPlanModal', { title: 'Modifier le Plan de Sièges' });
    } catch (error) {
        console.error('Erreur lors de la modification du plan:', error);
        operatorUIManager.showAlert('Erreur lors du chargement du plan', 'error');
    }
}

/**
 * Affiche un aperçu détaillé du plan de sièges
 * @param {number} planId - ID du plan
 */
function previewSeatPlan(planId) {
    const plan = allSeatPlans.find(p => p.id === planId);
    if (!plan) return;

    const previewHtml = `
        <div class="text-center">
            <h6>${plan.configuration}</h6>
            <p class="text-muted">${plan.rows} rangées × ${plan.columns} colonnes = ${plan.rows * plan.columns} sièges</p>
            
            <div class="seat-plan-full-preview">
                ${generateFullSeatPlanPreview(plan)}
            </div>
            
            <div class="mt-3">
                <div class="d-flex justify-content-center align-items-center">
                    <div class="seat-legend me-3">
                        <div class="seat-preview-seat me-1"></div>
                        <small>Siège</small>
                    </div>
                    <div class="seat-legend">
                        <div class="seat-preview-aisle me-1"></div>
                        <small>Allée</small>
                    </div>
                </div>
            </div>
        </div>
    `;

    const modalHtml = `
        <div class="modal fade" id="seatPlanPreviewModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Aperçu du Plan de Sièges</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${previewHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Supprime la modale existante
    const existingModal = document.getElementById('seatPlanPreviewModal');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    const modal = new bootstrap.Modal(document.getElementById('seatPlanPreviewModal'));
    modal.show();
}

/**
 * Génère un aperçu complet du plan de sièges
 * @param {Object} plan - Plan de sièges
 * @returns {string} - HTML de l'aperçu complet
 */
function generateFullSeatPlanPreview(plan) {
    let previewHtml = '<div class="seat-full-preview-grid">';
    
    for (let row = 0; row < plan.rows; row++) {
        previewHtml += '<div class="seat-full-preview-row">';
        
        for (let col = 0; col < plan.columns; col++) {
            const seatNumber = String.fromCharCode(65 + row) + (col + 1);
            
            // Ajoute une allée au milieu pour les configurations avec plus de 4 colonnes
            if (plan.columns > 4 && col === Math.floor(plan.columns / 2)) {
                previewHtml += '<div class="seat-preview-aisle"></div>';
            }
            
            previewHtml += `<div class="seat-full-preview-seat" title="Siège ${seatNumber}">${seatNumber}</div>`;
        }
        
        previewHtml += '</div>';
    }
    
    previewHtml += '</div>';
    
    return previewHtml;
}

/**
 * Supprime un plan de sièges
 * @param {number} planId - ID du plan
 */
async function deleteSeatPlan(planId) {
    const plan = allSeatPlans.find(p => p.id === planId);
    if (!plan) return;

    const confirmed = confirm(`Êtes-vous sûr de vouloir supprimer le plan "${plan.configuration}" ?`);
    if (!confirmed) return;

    try {
        await operatorApiClient.delete(`operator/seat-plans/${planId}`);
        operatorUIManager.showAlert('Plan de sièges supprimé avec succès', 'success');
        
        // Invalide le cache et recharge
        operatorDataCache.invalidateAfterUpdate('seatPlans');
        await loadSeatPlans();
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        operatorUIManager.showAlert('Erreur lors de la suppression', 'error');
    }
}

/**
 * Sauvegarde un plan de sièges (appelée par le gestionnaire de formulaires)
 */
async function saveSeatPlan() {
    const formData = operatorUIManager.getFormData('seatPlanForm');
    const isEdit = !!formData.seatPlanId;

    try {
        if (isEdit) {
            await operatorApiClient.put(`operator/seat-plans/${formData.seatPlanId}`, formData);
            operatorUIManager.showAlert('Plan de sièges mis à jour avec succès', 'success');
        } else {
            await operatorApiClient.post('operator/seat-plans', formData);
            operatorUIManager.showAlert('Plan de sièges créé avec succès', 'success');
        }

        operatorUIManager.hideModal('seatPlanModal');
        operatorDataCache.invalidateAfterUpdate('seatPlans');
        await loadSeatPlans();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        operatorUIManager.showAlert(`Erreur: ${error.message}`, 'error');
    }
}
</script>

<style>
.seat-preview-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.seat-preview-row {
    display: flex;
    gap: 2px;
}

.seat-preview-seat {
    width: 12px;
    height: 12px;
    background-color: #0d6efd;
    border-radius: 2px;
}

.seat-full-preview-grid {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.seat-full-preview-row {
    display: flex;
    gap: 5px;
    align-items: center;
}

.seat-full-preview-seat {
    width: 35px;
    height: 35px;
    background-color: #0d6efd;
    color: white;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.seat-preview-aisle {
    width: 20px;
    height: 12px;
    background-color: transparent;
}

.seat-legend {
    display: flex;
    align-items: center;
}
</style>
