<!-- Page Gestion des Arrêts -->
<div id="stopsPage">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Gestion des Arrêts</h3>
        <button class="btn btn-primary" onclick="showCreateStopModal()">
            <i class="fas fa-plus me-2"></i>Nouvel arrêt
        </button>
    </div>

    <!-- Filtres pour les arrêts -->
    <div class="chart-container mb-4">
        <div class="row">
            <div class="col-md-4">
                <label for="stopLocationFilter" class="form-label">Lieu</label>
                <select class="form-select" id="stopLocationFilter" onchange="filterStops()">
                    <option value="">Tous les lieux</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="stopSearchFilter" class="form-label">Rechercher</label>
                <input type="text" class="form-control" id="stopSearchFilter"
                       placeholder="Nom d'arrêt, adresse..." onkeyup="filterStops()">
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearStopFilters()">
                    <i class="fas fa-times"></i> Effacer
                </button>
            </div>
        </div>
    </div>

    <!-- Tableau des arrêts -->
    <div class="chart-container">
        <div id="stopsTableContainer">
            <div class="text-center py-4">
                <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                <p class="mt-2 text-muted">Chargement des arrêts...</p>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Variables globales pour la gestion des arrêts
 */
let allStops = [];
let filteredStops = [];
let allLocations = [];

/**
 * Initialisation de la page des arrêts
 */
async function initStopsPage() {
    try {
        await Promise.all([
            loadStops(),
            loadLocationsForFilter()
        ]);
        setupStopFilters();
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page des arrêts:', error);
        operatorUIManager.showAlert('Erreur lors du chargement des arrêts', 'error');
    }
}

/**
 * Charge la liste des arrêts
 */
async function loadStops() {
    const container = document.getElementById('stopsTableContainer');
    const loadingId = operatorUIManager.showLoading(container, 'Chargement des arrêts...');

    try {
        const data = await operatorDataCache.getStops();
        allStops = data.stops || [];
        filteredStops = [...allStops];
        
        displayStops();
        operatorUIManager.hideLoading(loadingId);
    } catch (error) {
        operatorUIManager.hideLoading(loadingId);
        throw error;
    }
}

/**
 * Charge les lieux pour le filtre
 */
async function loadLocationsForFilter() {
    try {
        const data = await operatorDataCache.getLocations();
        allLocations = data.locations || [];
        populateLocationFilter();
    } catch (error) {
        console.error('Erreur lors du chargement des lieux:', error);
    }
}

/**
 * Affiche la liste des arrêts
 */
function displayStops() {
    const container = document.getElementById('stopsTableContainer');
    
    const columns = [
        { field: 'name', title: 'Nom de l\'arrêt' },
        { 
            field: 'location_name', 
            title: 'Lieu',
            render: (value, row) => {
                const location = allLocations.find(l => l.id === row.location_id);
                return location ? location.name : value || '-';
            }
        },
        { field: 'address', title: 'Adresse' },
        { 
            field: 'coordinates',
            title: 'Coordonnées',
            render: (value, row) => {
                if (row.latitude && row.longitude) {
                    return `${row.latitude}, ${row.longitude}`;
                }
                return '-';
            }
        },
        {
            field: 'created_at',
            title: 'Créé le',
            render: (value) => new Date(value).toLocaleDateString('fr-FR')
        }
    ];

    const actions = [
        {
            icon: 'fas fa-edit',
            title: 'Modifier',
            variant: 'primary',
            onclick: 'editStop({id})'
        },
        {
            icon: 'fas fa-map',
            title: 'Voir sur la carte',
            variant: 'info',
            onclick: 'viewStopOnMap({id})'
        },
        {
            icon: 'fas fa-trash',
            title: 'Supprimer',
            variant: 'danger',
            onclick: 'deleteStop({id})'
        }
    ];

    const tableHtml = operatorUIManager.createTable(filteredStops, columns, { actions });
    container.innerHTML = tableHtml;
}

/**
 * Remplit le filtre des lieux
 */
function populateLocationFilter() {
    const locationFilter = document.getElementById('stopLocationFilter');
    if (!locationFilter) return;

    // Garde l'option par défaut
    const defaultOption = locationFilter.querySelector('option[value=""]');
    locationFilter.innerHTML = '';
    locationFilter.appendChild(defaultOption);
    
    allLocations.forEach(location => {
        const option = document.createElement('option');
        option.value = location.id;
        option.textContent = location.name;
        locationFilter.appendChild(option);
    });
}

/**
 * Configure les filtres
 */
function setupStopFilters() {
    // Écouteur pour la recherche en temps réel
    const searchInput = document.getElementById('stopSearchFilter');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(filterStops, 300);
        });
    }
}

/**
 * Filtre les arrêts selon les critères
 */
function filterStops() {
    const locationFilter = document.getElementById('stopLocationFilter')?.value || '';
    const searchFilter = document.getElementById('stopSearchFilter')?.value.toLowerCase() || '';

    filteredStops = allStops.filter(stop => {
        const matchesLocation = !locationFilter || stop.location_id == locationFilter;
        const matchesSearch = !searchFilter || 
            stop.name.toLowerCase().includes(searchFilter) ||
            (stop.address && stop.address.toLowerCase().includes(searchFilter));

        return matchesLocation && matchesSearch;
    });

    displayStops();
}

/**
 * Filtre les arrêts par lieu (appelée depuis d'autres pages)
 * @param {number} locationId - ID du lieu
 */
function filterStopsByLocation(locationId) {
    const locationFilter = document.getElementById('stopLocationFilter');
    if (locationFilter) {
        locationFilter.value = locationId;
        filterStops();
    }
}

/**
 * Efface tous les filtres
 */
function clearStopFilters() {
    document.getElementById('stopLocationFilter').value = '';
    document.getElementById('stopSearchFilter').value = '';
    
    filteredStops = [...allStops];
    displayStops();
}

/**
 * Affiche la modale de création d'un arrêt
 */
async function showCreateStopModal() {
    try {
        operatorUIManager.resetForm('stopForm');
        await operatorFormHandler.loadFormDependencies('stopForm');
        operatorUIManager.showModal('stopModal', { title: 'Nouvel Arrêt' });
    } catch (error) {
        console.error('Erreur lors de l\'ouverture de la modale:', error);
        operatorUIManager.showAlert('Erreur lors de l\'ouverture du formulaire', 'error');
    }
}

/**
 * Modifie un arrêt
 * @param {number} stopId - ID de l'arrêt
 */
async function editStop(stopId) {
    try {
        const stop = allStops.find(s => s.id === stopId);
        if (!stop) {
            operatorUIManager.showAlert('Arrêt non trouvé', 'error');
            return;
        }

        await operatorFormHandler.prepareFormForEdit('stopForm', stop);
        operatorUIManager.showModal('stopModal', { title: 'Modifier l\'Arrêt' });
    } catch (error) {
        console.error('Erreur lors de la modification de l\'arrêt:', error);
        operatorUIManager.showAlert('Erreur lors du chargement de l\'arrêt', 'error');
    }
}

/**
 * Supprime un arrêt
 * @param {number} stopId - ID de l'arrêt
 */
async function deleteStop(stopId) {
    const stop = allStops.find(s => s.id === stopId);
    if (!stop) return;

    const confirmed = confirm(`Êtes-vous sûr de vouloir supprimer l'arrêt "${stop.name}" ?`);
    if (!confirmed) return;

    try {
        await operatorApiClient.deleteStop(stopId);
        operatorUIManager.showAlert('Arrêt supprimé avec succès', 'success');
        
        // Invalide le cache et recharge
        operatorDataCache.invalidateAfterUpdate('stops');
        await loadStops();
    } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        operatorUIManager.showAlert('Erreur lors de la suppression', 'error');
    }
}

/**
 * Affiche un arrêt sur la carte
 * @param {number} stopId - ID de l'arrêt
 */
function viewStopOnMap(stopId) {
    const stop = allStops.find(s => s.id === stopId);
    if (!stop || !stop.latitude || !stop.longitude) {
        operatorUIManager.showAlert('Coordonnées non disponibles pour cet arrêt', 'warning');
        return;
    }

    // Ouvre Google Maps avec les coordonnées
    const url = `https://www.google.com/maps?q=${stop.latitude},${stop.longitude}`;
    window.open(url, '_blank');
}

/**
 * Sauvegarde un arrêt (appelée par le gestionnaire de formulaires)
 */
async function saveStop() {
    const formData = operatorUIManager.getFormData('stopForm');
    const isEdit = !!formData.stopId;

    try {
        if (isEdit) {
            await operatorApiClient.updateStop(formData.stopId, formData);
            operatorUIManager.showAlert('Arrêt mis à jour avec succès', 'success');
        } else {
            await operatorApiClient.createStop(formData);
            operatorUIManager.showAlert('Arrêt créé avec succès', 'success');
        }

        operatorUIManager.hideModal('stopModal');
        operatorDataCache.invalidateAfterUpdate('stops');
        await loadStops();

    } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        operatorUIManager.showAlert(`Erreur: ${error.message}`, 'error');
    }
}
</script>
