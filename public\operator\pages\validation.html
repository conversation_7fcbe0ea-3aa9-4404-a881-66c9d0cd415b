<!-- Page Validation des tickets -->
<div id="validationPage">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="chart-container text-center">
                <h3 class="mb-4"><i class="fas fa-qrcode me-2"></i>Validation des tickets</h3>
                
                <div class="mb-4">
                    <div class="input-group input-group-lg">
                        <input type="text" class="form-control" id="ticketCodeInput" 
                               placeholder="Scanner ou saisir le code du ticket" autofocus>
                        <button class="btn btn-primary" type="button" onclick="validateTicket()">
                            <i class="fas fa-check me-2"></i>Valider
                        </button>
                    </div>
                    <div class="form-text mt-2">
                        Sai<PERSON><PERSON>z le code du ticket ou utilisez le scanner QR ci-dessous
                    </div>
                </div>

                <div id="validationResult" class="mt-4"></div>

                <div class="mt-4">
                    <button class="btn btn-outline-secondary me-2" onclick="startQRScanner()">
                        <i class="fas fa-camera me-2"></i>Scanner QR Code
                    </button>
                    <button class="btn btn-outline-info me-2" onclick="showValidationHistory()">
                        <i class="fas fa-history me-2"></i>Historique
                    </button>
                    <button class="btn btn-outline-success" onclick="showValidationStats()">
                        <i class="fas fa-chart-pie me-2"></i>Statistiques
                    </button>
                </div>

                <!-- Statistiques rapides -->
                <div class="row mt-5">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <h4 class="text-success" id="validTicketsToday">0</h4>
                                <p class="text-muted mb-0">Tickets valides aujourd'hui</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <i class="fas fa-times-circle fa-2x text-danger mb-2"></i>
                                <h4 class="text-danger" id="invalidTicketsToday">0</h4>
                                <p class="text-muted mb-0">Tickets invalides aujourd'hui</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h4 class="text-info" id="totalValidationsToday">0</h4>
                                <p class="text-muted mb-0">Total validations aujourd'hui</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions d'utilisation -->
                <div class="mt-5">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title"><i class="fas fa-info-circle me-2"></i>Instructions</h6>
                            <div class="row text-start">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-keyboard text-primary me-2"></i>Saisissez le code manuellement</li>
                                        <li><i class="fas fa-camera text-primary me-2"></i>Utilisez le scanner QR</li>
                                        <li><i class="fas fa-enter text-primary me-2"></i>Appuyez sur Entrée pour valider</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success me-2"></i>Ticket valide = accès autorisé</li>
                                        <li><i class="fas fa-times text-danger me-2"></i>Ticket invalide = accès refusé</li>
                                        <li><i class="fas fa-history text-info me-2"></i>Consultez l'historique</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
/**
 * Variables globales pour la validation
 */
let validationStats = {
    validToday: 0,
    invalidToday: 0,
    totalToday: 0
};

/**
 * Initialisation de la page de validation
 */
async function initValidationPage() {
    try {
        // Initialise le scanner de validation
        operatorValidationScanner.initialize();
        
        // Charge les statistiques
        await loadValidationStats();
        
        // Configure l'auto-focus
        setupAutoFocus();
        
        // Configure les raccourcis clavier
        setupValidationKeyboardShortcuts();
        
    } catch (error) {
        console.error('Erreur lors de l\'initialisation de la page de validation:', error);
        operatorUIManager.showAlert('Erreur lors de l\'initialisation', 'error');
    }
}

/**
 * Charge les statistiques de validation
 */
async function loadValidationStats() {
    try {
        const data = await operatorApiClient.get('operator/validation/stats');
        
        validationStats = {
            validToday: data.valid_today || 0,
            invalidToday: data.invalid_today || 0,
            totalToday: data.total_today || 0
        };
        
        updateStatsDisplay();
    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
        // Continue sans les statistiques
    }
}

/**
 * Met à jour l'affichage des statistiques
 */
function updateStatsDisplay() {
    const validElement = document.getElementById('validTicketsToday');
    const invalidElement = document.getElementById('invalidTicketsToday');
    const totalElement = document.getElementById('totalValidationsToday');
    
    if (validElement) validElement.textContent = validationStats.validToday;
    if (invalidElement) invalidElement.textContent = validationStats.invalidToday;
    if (totalElement) totalElement.textContent = validationStats.totalToday;
}

/**
 * Configure l'auto-focus sur le champ de saisie
 */
function setupAutoFocus() {
    const ticketInput = document.getElementById('ticketCodeInput');
    if (ticketInput) {
        // Focus initial
        ticketInput.focus();
        
        // Re-focus après validation
        const observer = new MutationObserver(() => {
            if (document.getElementById('validationResult').innerHTML === '') {
                setTimeout(() => ticketInput.focus(), 100);
            }
        });
        
        observer.observe(document.getElementById('validationResult'), {
            childList: true,
            subtree: true
        });
    }
}

/**
 * Configure les raccourcis clavier pour la validation
 */
function setupValidationKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // F2 : Scanner QR
        if (e.key === 'F2') {
            e.preventDefault();
            startQRScanner();
        }
        
        // F3 : Historique
        if (e.key === 'F3') {
            e.preventDefault();
            showValidationHistory();
        }
        
        // F4 : Statistiques
        if (e.key === 'F4') {
            e.preventDefault();
            showValidationStats();
        }
        
        // Échap : Vider le champ et le résultat
        if (e.key === 'Escape') {
            const ticketInput = document.getElementById('ticketCodeInput');
            const resultContainer = document.getElementById('validationResult');
            
            if (ticketInput) ticketInput.value = '';
            if (resultContainer) resultContainer.innerHTML = '';
            
            if (ticketInput) ticketInput.focus();
        }
    });
}

/**
 * Valide un ticket (fonction wrapper)
 */
async function validateTicket() {
    try {
        await operatorValidationScanner.validateTicket();
        
        // Met à jour les statistiques après validation
        setTimeout(async () => {
            await loadValidationStats();
        }, 1000);
        
    } catch (error) {
        console.error('Erreur lors de la validation:', error);
        operatorUIManager.showAlert('Erreur lors de la validation', 'error');
    }
}

/**
 * Démarre le scanner QR (fonction wrapper)
 */
function startQRScanner() {
    operatorValidationScanner.startQRScanner();
}

/**
 * Affiche l'historique de validation (fonction wrapper)
 */
function showValidationHistory() {
    operatorValidationScanner.showValidationHistory();
}

/**
 * Affiche les statistiques détaillées de validation
 */
async function showValidationStats() {
    try {
        const data = await operatorApiClient.get('operator/validation/detailed-stats');
        
        const statsContent = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Statistiques du jour</h6>
                    <table class="table table-sm">
                        <tr><td>Tickets valides</td><td class="text-success fw-bold">${data.today.valid || 0}</td></tr>
                        <tr><td>Tickets invalides</td><td class="text-danger fw-bold">${data.today.invalid || 0}</td></tr>
                        <tr><td>Tickets expirés</td><td class="text-warning fw-bold">${data.today.expired || 0}</td></tr>
                        <tr><td>Tickets déjà utilisés</td><td class="text-secondary fw-bold">${data.today.used || 0}</td></tr>
                        <tr><td><strong>Total</strong></td><td class="fw-bold">${data.today.total || 0}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Statistiques de la semaine</h6>
                    <table class="table table-sm">
                        <tr><td>Tickets valides</td><td class="text-success fw-bold">${data.week.valid || 0}</td></tr>
                        <tr><td>Tickets invalides</td><td class="text-danger fw-bold">${data.week.invalid || 0}</td></tr>
                        <tr><td>Taux de réussite</td><td class="fw-bold">${data.week.success_rate || 0}%</td></tr>
                        <tr><td><strong>Total</strong></td><td class="fw-bold">${data.week.total || 0}</td></tr>
                    </table>
                </div>
            </div>
            
            <div class="mt-3">
                <h6>Validations par heure (aujourd'hui)</h6>
                <canvas id="hourlyValidationChart" height="100"></canvas>
            </div>
        `;

        const modalHtml = `
            <div class="modal fade" id="validationStatsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Statistiques de validation</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${statsContent}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" onclick="exportValidationStats()">
                                <i class="fas fa-download me-2"></i>Exporter
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprime la modale existante
        const existingModal = document.getElementById('validationStatsModal');
        if (existingModal) {
            existingModal.remove();
        }

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        const modal = new bootstrap.Modal(document.getElementById('validationStatsModal'));
        modal.show();

        // Crée le graphique des validations par heure
        setTimeout(() => {
            createHourlyValidationChart(data.hourly || []);
        }, 300);

    } catch (error) {
        console.error('Erreur lors du chargement des statistiques:', error);
        operatorUIManager.showAlert('Erreur lors du chargement des statistiques', 'error');
    }
}

/**
 * Crée le graphique des validations par heure
 * @param {Array} hourlyData - Données par heure
 */
function createHourlyValidationChart(hourlyData) {
    const canvas = document.getElementById('hourlyValidationChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: hourlyData.map(item => `${item.hour}h`),
            datasets: [{
                label: 'Validations',
                data: hourlyData.map(item => item.count),
                backgroundColor: 'rgba(13, 110, 253, 0.8)',
                borderColor: '#0d6efd',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });
}

/**
 * Exporte les statistiques de validation
 */
async function exportValidationStats() {
    try {
        const data = await operatorApiClient.get('operator/validation/export');
        
        const csvContent = [
            ['Date', 'Heure', 'Code Ticket', 'Statut', 'Passager', 'Itinéraire'],
            ...data.validations.map(validation => [
                new Date(validation.timestamp).toLocaleDateString('fr-FR'),
                new Date(validation.timestamp).toLocaleTimeString('fr-FR'),
                validation.ticket_code,
                validation.status,
                validation.passenger_name || '',
                validation.route_name || ''
            ])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `validations_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        operatorUIManager.showAlert('Statistiques exportées avec succès', 'success');
    } catch (error) {
        console.error('Erreur lors de l\'export:', error);
        operatorUIManager.showAlert('Erreur lors de l\'export', 'error');
    }
}

/**
 * Nettoie la page avant de quitter
 */
function cleanupValidationPage() {
    operatorValidationScanner.cleanup();
}
</script>
